const eslintRules = {
  "array-callback-return": "off",
  "block-scoped-var": "off",
  curly: "off",
  "default-case": "off",
  "default-case-last": "off",
  "default-param-last": "off",
  eqeqeq: "off",
  "for-direction": "off",
  "func-style": "off",
  "func-names": "off",
  "grouped-accessor-pairs": "off",
  "guard-for-in": "off",
  "init-declarations": "off",
  "max-classes-per-file": "off",
  "max-depth": "off",
  "max-lines-per-function": "off",
  "max-lines": "off",
  "max-nested-callbacks": "off",
  "max-params": "off",
  "new-cap": "off",
  "no-extra-bind": "off",
  "no-alert": "off",
  "no-array-constructor": "off",
  "no-async-promise-executor": "off",
  "no-await-in-loop": "off",
  "no-bitwise": "off",
  "no-caller": "off",
  "no-case-declarations": "off",
  "no-class-assign": "off",
  "no-duplicate-imports": "off",
  "no-extra-label": "off",
  "no-labels": "off",
  "no-lone-blocks": "off",
  "no-lonely-if": "off",
  "no-multi-assign": "off",
  "no-nested-ternary": "off",
  "no-object-constructor": "off",
  "no-restricted-imports": "off",
  "no-unneeded-ternary": "off",
  "no-useless-backreference": "off",
  "no-useless-call": "off",
  "no-compare-neg-zero": "off",
  "no-cond-assign": "off",
  "no-console": "off",
  "no-const-assign": "off",
  "no-constant-binary-expression": "off",
  "no-constant-condition": "off",
  "no-constructor-return": "off",
  "no-continue": "off",
  "no-control-regex": "off",
  "no-debugger": "off",
  "no-delete-var": "off",
  "no-div-regex": "off",
  "no-dupe-class-members": "off",
  "no-dupe-else-if": "off",
  "no-dupe-keys": "off",
  "no-duplicate-case": "off",
  "no-else-return": "off",
  "no-empty-character-class": "off",
  "no-empty-function": "off",
  "no-empty-pattern": "off",
  "no-empty-static-block": "off",
  "no-empty": "off",
  "no-eq-null": "off",
  "no-eval": "off",
  "no-ex-assign": "off",
  "no-extend-native": "off",
  "no-extra-boolean-cast": "off",
  "no-fallthrough": "off",
  "no-func-assign": "off",
  "no-global-assign": "off",
  "no-import-assign": "off",
  "no-inner-declarations": "off",
  "no-invalid-regexp": "off",
  "no-irregular-whitespace": "off",
  "no-iterator": "off",
  "no-label-var": "off",
  "no-loss-of-precision": "off",
  "no-magic-numbers": "off",
  "no-negated-condition": "off",
  "no-multi-str": "off",
  "no-new-func": "off",
  "no-new-native-nonconstructor": "off",
  "no-new-wrappers": "off",
  "no-new": "off",
  "no-nonoctal-decimal-escape": "off",
  "no-obj-calls": "off",
  "no-plusplus": "off",
  "no-proto": "off",
  "no-prototype-builtins": "off",
  "no-redeclare": "off",
  "no-regex-spaces": "off",
  "no-restricted-globals": "off",
  "no-return-assign": "off",
  "no-script-url": "off",
  "no-self-assign": "off",
  "no-self-compare": "off",
  "no-setter-return": "off",
  "no-shadow-restricted-names": "off",
  "no-sparse-arrays": "off",
  "no-template-curly-in-string": "off",
  "no-ternary": "off",
  "no-this-before-super": "off",
  "no-throw-literal": "off",
  "no-undefined": "off",
  "no-unexpected-multiline": "off",
  "no-unsafe-finally": "off",
  "no-unsafe-negation": "off",
  "no-unsafe-optional-chaining": "off",
  "no-unused-expressions": "off",
  "no-unused-labels": "off",
  "no-unused-private-class-members": "off",
  "no-unused-vars": "off",
  "no-useless-catch": "off",
  "no-useless-concat": "off",
  "no-useless-constructor": "off",
  "no-useless-escape": "off",
  "no-useless-rename": "off",
  "no-var": "off",
  "no-void": "off",
  "no-with": "off",
  "operator-assignment": "off",
  "prefer-promise-reject-errors": "off",
  "prefer-exponentiation-operator": "off",
  "prefer-numeric-literals": "off",
  "prefer-object-has-own": "off",
  "prefer-object-spread": "off",
  "prefer-rest-params": "off",
  "prefer-spread": "off",
  radix: "off",
  "require-await": "off",
  "require-yield": "off",
  "sort-imports": "off",
  "sort-keys": "off",
  "sort-vars": "off",
  "symbol-description": "off",
  "unicode-bom": "off",
  "use-isnan": "off",
  "valid-typeof": "off",
  "vars-on-top": "off",
  yoda: "off"
};
const importRules = {
  "import/consistent-type-specifier-style": "off",
  "import/default": "off",
  "import/exports-last": "off",
  "import/first": "off",
  "import/group-exports": "off",
  "import/no-unassigned-import": "off",
  "import/no-empty-named-blocks": "off",
  "import/no-anonymous-default-export": "off",
  "import/no-absolute-path": "off",
  "import/no-mutable-exports": "off",
  "import/no-named-default": "off",
  "import/no-namespace": "off",
  "import/max-dependencies": "off",
  "import/namespace": "off",
  "import/no-amd": "off",
  "import/no-commonjs": "off",
  "import/no-cycle": "off",
  "import/no-default-export": "off",
  "import/no-duplicates": "off",
  "import/no-dynamic-require": "off",
  "import/no-named-as-default": "off",
  "import/no-named-as-default-member": "off",
  "import/no-self-import": "off",
  "import/no-webpack-loader-syntax": "off",
  "import/unambiguous": "off"
};
const jestRules = {
  "jest/consistent-test-it": "off",
  "jest/expect-expect": "off",
  "jest/max-expects": "off",
  "jest/max-nested-describe": "off",
  "jest/no-alias-methods": "off",
  "jest/no-commented-out-tests": "off",
  "jest/no-conditional-expect": "off",
  "jest/no-conditional-in-test": "off",
  "jest/no-confusing-set-timeout": "off",
  "jest/no-deprecated-functions": "off",
  "jest/no-disabled-tests": "off",
  "jest/no-done-callback": "off",
  "jest/no-duplicate-hooks": "off",
  "jest/no-export": "off",
  "jest/no-focused-tests": "off",
  "jest/no-hooks": "off",
  "jest/no-identical-title": "off",
  "jest/no-interpolation-in-snapshots": "off",
  "jest/no-jasmine-globals": "off",
  "jest/no-large-snapshots": "off",
  "jest/no-mocks-import": "off",
  "jest/no-restricted-jest-methods": "off",
  "jest/no-restricted-matchers": "off",
  "jest/no-standalone-expect": "off",
  "jest/no-test-prefixes": "off",
  "jest/no-test-return-statement": "off",
  "jest/no-untyped-mock-factory": "off",
  "jest/prefer-each": "off",
  "jest/prefer-called-with": "off",
  "jest/prefer-comparison-matcher": "off",
  "jest/prefer-equality-matcher": "off",
  "jest/prefer-expect-resolves": "off",
  "jest/prefer-hooks-in-order": "off",
  "jest/prefer-hooks-on-top": "off",
  "jest/prefer-jest-mocked": "off",
  "jest/prefer-lowercase-title": "off",
  "jest/prefer-mock-promise-shorthand": "off",
  "jest/prefer-spy-on": "off",
  "jest/prefer-strict-equal": "off",
  "jest/prefer-to-be": "off",
  "jest/prefer-to-contain": "off",
  "jest/prefer-to-have-length": "off",
  "jest/prefer-todo": "off",
  "jest/require-hook": "off",
  "jest/require-to-throw-message": "off",
  "jest/require-top-level-describe": "off",
  "jest/valid-describe-callback": "off",
  "jest/valid-expect": "off",
  "jest/valid-title": "off"
};
const jsdocRules = {
  "jsdoc/check-access": "off",
  "jsdoc/check-property-names": "off",
  "jsdoc/check-tag-names": "off",
  "jsdoc/empty-tags": "off",
  "jsdoc/implements-on-classes": "off",
  "jsdoc/no-defaults": "off",
  "jsdoc/require-param": "off",
  "jsdoc/require-param-description": "off",
  "jsdoc/require-param-name": "off",
  "jsdoc/require-param-type": "off",
  "jsdoc/require-property": "off",
  "jsdoc/require-property-description": "off",
  "jsdoc/require-property-name": "off",
  "jsdoc/require-property-type": "off",
  "jsdoc/require-returns": "off",
  "jsdoc/require-returns-description": "off",
  "jsdoc/require-returns-type": "off",
  "jsdoc/require-yields": "off"
};
const jsxA11yRules = {
  "jsx-a11y/alt-text": "off",
  "jsx-a11y/anchor-has-content": "off",
  "jsx-a11y/anchor-is-valid": "off",
  "jsx-a11y/aria-activedescendant-has-tabindex": "off",
  "jsx-a11y/aria-props": "off",
  "jsx-a11y/aria-role": "off",
  "jsx-a11y/aria-unsupported-elements": "off",
  "jsx-a11y/autocomplete-valid": "off",
  "jsx-a11y/click-events-have-key-events": "off",
  "jsx-a11y/heading-has-content": "off",
  "jsx-a11y/html-has-lang": "off",
  "jsx-a11y/iframe-has-title": "off",
  "jsx-a11y/img-redundant-alt": "off",
  "jsx-a11y/label-has-associated-control": "off",
  "jsx-a11y/lang": "off",
  "jsx-a11y/media-has-caption": "off",
  "jsx-a11y/mouse-events-have-key-events": "off",
  "jsx-a11y/no-noninteractive-tabindex": "off",
  "jsx-a11y/no-access-key": "off",
  "jsx-a11y/no-aria-hidden-on-focusable": "off",
  "jsx-a11y/no-autofocus": "off",
  "jsx-a11y/no-distracting-elements": "off",
  "jsx-a11y/no-redundant-roles": "off",
  "jsx-a11y/prefer-tag-over-role": "off",
  "jsx-a11y/role-has-required-aria-props": "off",
  "jsx-a11y/role-supports-aria-props": "off",
  "jsx-a11y/scope": "off",
  "jsx-a11y/tabindex-no-positive": "off",
  "jsx-a11y/anchor-ambiguous-text": "off"
};
const nextjsRules = {
  "@next/next/google-font-display": "off",
  "@next/next/google-font-preconnect": "off",
  "@next/next/inline-script-id": "off",
  "@next/next/next-script-for-ga": "off",
  "@next/next/no-assign-module-variable": "off",
  "@next/next/no-async-client-component": "off",
  "@next/next/no-before-interactive-script-outside-document": "off",
  "@next/next/no-css-tags": "off",
  "@next/next/no-document-import-in-page": "off",
  "@next/next/no-duplicate-head": "off",
  "@next/next/no-head-element": "off",
  "@next/next/no-head-import-in-document": "off",
  "@next/next/no-img-element": "off",
  "@next/next/no-page-custom-font": "off",
  "@next/next/no-script-component-in-head": "off",
  "@next/next/no-styled-jsx-in-document": "off",
  "@next/next/no-sync-scripts": "off",
  "@next/next/no-title-in-document-head": "off",
  "@next/next/no-typos": "off",
  "@next/next/no-unwanted-polyfillio": "off"
};
const nodeRules = {
  "node/no-exports-assign": "off",
  "node/no-new-require": "off"
};
const promiseRules = {
  "promise/avoid-new": "off",
  "promise/catch-or-return": "off",
  "promise/no-return-wrap": "off",
  "promise/no-nesting": "off",
  "promise/no-promise-in-callback": "off",
  "promise/no-callback-in-promise": "off",
  "promise/no-new-statics": "off",
  "promise/param-names": "off",
  "promise/prefer-catch": "off",
  "promise/prefer-await-to-callbacks": "off",
  "promise/prefer-await-to-then": "off",
  "promise/spec-only": "off",
  "promise/valid-params": "off"
};
const reactRules = {
  "react/button-has-type": "off",
  "react/checked-requires-onchange-or-readonly": "off",
  "react/forbid-elements": "off",
  "react/forward-ref-uses-ref": "off",
  "react/iframe-missing-sandbox": "off",
  "react/jsx-filename-extension": "off",
  "react/jsx-boolean-value": "off",
  "react/jsx-curly-brace-presence": "off",
  "react/jsx-key": "off",
  "react/jsx-no-comment-textnodes": "off",
  "react/jsx-no-duplicate-props": "off",
  "react/jsx-no-script-url": "off",
  "react/jsx-no-target-blank": "off",
  "react/jsx-no-undef": "off",
  "react/jsx-no-useless-fragment": "off",
  "react/jsx-props-no-spread-multi": "off",
  "react/no-namespace": "off",
  "react/no-array-index-key": "off",
  "react/no-children-prop": "off",
  "react/no-danger-with-children": "off",
  "react/no-danger": "off",
  "react/no-direct-mutation-state": "off",
  "react/no-find-dom-node": "off",
  "react/no-is-mounted": "off",
  "react/no-render-return-value": "off",
  "react/no-set-state": "off",
  "react/no-string-refs": "off",
  "react/no-unescaped-entities": "off",
  "react/no-unknown-property": "off",
  "react/prefer-es6-class": "off",
  "react/react-in-jsx-scope": "off",
  "react/self-closing-comp": "off",
  "react/style-prop-object": "off",
  "react/void-dom-elements-no-children": "off"
};
const reactHooksRules = {
  "react-hooks/exhaustive-deps": "off",
  "react-hooks/rules-of-hooks": "off"
};
const reactPerfRules = {
  "react-perf/jsx-no-jsx-as-prop": "off",
  "react-perf/jsx-no-new-array-as-prop": "off",
  "react-perf/jsx-no-new-function-as-prop": "off",
  "react-perf/jsx-no-new-object-as-prop": "off"
};
const typescriptRules = {
  "@typescript-eslint/adjacent-overload-signatures": "off",
  "@typescript-eslint/array-type": "off",
  "@typescript-eslint/ban-ts-comment": "off",
  "@typescript-eslint/ban-tslint-comment": "off",
  "@typescript-eslint/ban-types": "off",
  "@typescript-eslint/consistent-generic-constructors": "off",
  "@typescript-eslint/consistent-indexed-object-style": "off",
  "@typescript-eslint/consistent-type-definitions": "off",
  "@typescript-eslint/consistent-type-imports": "off",
  "@typescript-eslint/explicit-function-return-type": "off",
  "@typescript-eslint/no-inferrable-types": "off",
  "@typescript-eslint/no-confusing-non-null-assertion": "off",
  "@typescript-eslint/no-duplicate-enum-values": "off",
  "@typescript-eslint/no-dynamic-delete": "off",
  "@typescript-eslint/no-empty-interface": "off",
  "@typescript-eslint/no-empty-object-type": "off",
  "@typescript-eslint/no-explicit-any": "off",
  "@typescript-eslint/no-extra-non-null-assertion": "off",
  "@typescript-eslint/no-extraneous-class": "off",
  "@typescript-eslint/no-import-type-side-effects": "off",
  "@typescript-eslint/no-misused-new": "off",
  "@typescript-eslint/no-namespace": "off",
  "@typescript-eslint/no-non-null-asserted-nullish-coalescing": "off",
  "@typescript-eslint/no-non-null-asserted-optional-chain": "off",
  "@typescript-eslint/no-non-null-assertion": "off",
  "@typescript-eslint/no-require-imports": "off",
  "@typescript-eslint/no-this-alias": "off",
  "@typescript-eslint/no-unnecessary-parameter-property-assignment": "off",
  "@typescript-eslint/no-unnecessary-type-constraint": "off",
  "@typescript-eslint/no-unsafe-declaration-merging": "off",
  "@typescript-eslint/no-unsafe-function-type": "off",
  "@typescript-eslint/no-useless-empty-export": "off",
  "@typescript-eslint/no-var-requires": "off",
  "@typescript-eslint/no-wrapper-object-types": "off",
  "@typescript-eslint/prefer-as-const": "off",
  "@typescript-eslint/prefer-enum-initializers": "off",
  "@typescript-eslint/prefer-for-of": "off",
  "@typescript-eslint/prefer-function-type": "off",
  "@typescript-eslint/prefer-literal-enum-member": "off",
  "@typescript-eslint/prefer-namespace-keyword": "off",
  "@typescript-eslint/prefer-ts-expect-error": "off",
  "@typescript-eslint/triple-slash-reference": "off",
  "@typescript-eslint/default-param-last": "off",
  "@typescript-eslint/init-declarations": "off",
  "@typescript-eslint/max-params": "off",
  "@typescript-eslint/no-array-constructor": "off",
  "@typescript-eslint/no-restricted-imports": "off",
  "@typescript-eslint/no-dupe-class-members": "off",
  "@typescript-eslint/no-empty-function": "off",
  "@typescript-eslint/no-loss-of-precision": "off",
  "@typescript-eslint/no-magic-numbers": "off",
  "@typescript-eslint/no-redeclare": "off",
  "@typescript-eslint/no-unused-expressions": "off",
  "@typescript-eslint/no-unused-vars": "off",
  "@typescript-eslint/no-useless-constructor": "off"
};
const unicornRules = {
  "unicorn/catch-error-name": "off",
  "unicorn/consistent-assert": "off",
  "unicorn/consistent-date-clone": "off",
  "unicorn/consistent-empty-array-spread": "off",
  "unicorn/consistent-existence-index-check": "off",
  "unicorn/consistent-function-scoping": "off",
  "unicorn/empty-brace-spaces": "off",
  "unicorn/error-message": "off",
  "unicorn/escape-case": "off",
  "unicorn/explicit-length-check": "off",
  "unicorn/filename-case": "off",
  "unicorn/new-for-builtins": "off",
  "unicorn/no-instanceof-builtins": "off",
  "unicorn/no-array-method-this-argument": "off",
  "unicorn/no-unnecessary-array-flat-depth": "off",
  "unicorn/no-unnecessary-slice-end": "off",
  "unicorn/no-accessor-recursion": "off",
  "unicorn/no-invalid-fetch-options": "off",
  "unicorn/no-abusive-eslint-disable": "off",
  "unicorn/no-anonymous-default-export": "off",
  "unicorn/no-array-for-each": "off",
  "unicorn/no-array-reduce": "off",
  "unicorn/no-await-expression-member": "off",
  "unicorn/no-await-in-promise-methods": "off",
  "unicorn/no-console-spaces": "off",
  "unicorn/no-document-cookie": "off",
  "unicorn/no-empty-file": "off",
  "unicorn/no-hex-escape": "off",
  "unicorn/no-instanceof-array": "off",
  "unicorn/no-invalid-remove-event-listener": "off",
  "unicorn/no-length-as-slice-end": "off",
  "unicorn/no-lonely-if": "off",
  "unicorn/no-magic-array-flat-depth": "off",
  "unicorn/no-negation-in-equality-check": "off",
  "unicorn/no-nested-ternary": "off",
  "unicorn/no-new-array": "off",
  "unicorn/no-new-buffer": "off",
  "unicorn/no-null": "off",
  "unicorn/no-object-as-default-parameter": "off",
  "unicorn/no-process-exit": "off",
  "unicorn/no-single-promise-in-promise-methods": "off",
  "unicorn/no-static-only-class": "off",
  "unicorn/no-thenable": "off",
  "unicorn/no-this-assignment": "off",
  "unicorn/no-typeof-undefined": "off",
  "unicorn/no-unnecessary-await": "off",
  "unicorn/no-unreadable-array-destructuring": "off",
  "unicorn/no-unreadable-iife": "off",
  "unicorn/no-useless-fallback-in-spread": "off",
  "unicorn/no-useless-length-check": "off",
  "unicorn/no-useless-promise-resolve-reject": "off",
  "unicorn/no-useless-spread": "off",
  "unicorn/no-useless-switch-case": "off",
  "unicorn/no-useless-undefined": "off",
  "unicorn/no-zero-fractions": "off",
  "unicorn/number-literal-case": "off",
  "unicorn/numeric-separators-style": "off",
  "unicorn/prefer-global-this": "off",
  "unicorn/prefer-object-from-entries": "off",
  "unicorn/prefer-array-find": "off",
  "unicorn/prefer-array-index-of": "off",
  "unicorn/prefer-spread": "off",
  "unicorn/prefer-add-event-listener": "off",
  "unicorn/prefer-array-flat": "off",
  "unicorn/prefer-array-flat-map": "off",
  "unicorn/prefer-array-some": "off",
  "unicorn/prefer-blob-reading-methods": "off",
  "unicorn/prefer-code-point": "off",
  "unicorn/prefer-date-now": "off",
  "unicorn/prefer-dom-node-append": "off",
  "unicorn/prefer-dom-node-dataset": "off",
  "unicorn/prefer-dom-node-remove": "off",
  "unicorn/prefer-dom-node-text-content": "off",
  "unicorn/prefer-event-target": "off",
  "unicorn/prefer-includes": "off",
  "unicorn/prefer-logical-operator-over-ternary": "off",
  "unicorn/prefer-math-min-max": "off",
  "unicorn/prefer-math-trunc": "off",
  "unicorn/prefer-modern-dom-apis": "off",
  "unicorn/prefer-modern-math-apis": "off",
  "unicorn/prefer-native-coercion-functions": "off",
  "unicorn/prefer-negative-index": "off",
  "unicorn/prefer-node-protocol": "off",
  "unicorn/prefer-number-properties": "off",
  "unicorn/prefer-optional-catch-binding": "off",
  "unicorn/prefer-prototype-methods": "off",
  "unicorn/prefer-query-selector": "off",
  "unicorn/prefer-reflect-apply": "off",
  "unicorn/prefer-regexp-test": "off",
  "unicorn/prefer-set-has": "off",
  "unicorn/prefer-set-size": "off",
  "unicorn/prefer-string-raw": "off",
  "unicorn/prefer-string-replace-all": "off",
  "unicorn/prefer-string-slice": "off",
  "unicorn/prefer-string-starts-ends-with": "off",
  "unicorn/prefer-string-trim-start-end": "off",
  "unicorn/prefer-structured-clone": "off",
  "unicorn/prefer-type-error": "off",
  "unicorn/require-post-message-target-origin": "off",
  "unicorn/require-array-join-separator": "off",
  "unicorn/require-number-to-fixed-digits-argument": "off",
  "unicorn/switch-case-braces": "off",
  "unicorn/text-encoding-identifier-case": "off",
  "unicorn/throw-new-error": "off",
  "unicorn/no-negated-condition": "off"
};
const vitestRules = {
  "vitest/no-conditional-tests": "off",
  "vitest/no-import-node-test": "off",
  "vitest/prefer-to-be-falsy": "off",
  "vitest/prefer-to-be-object": "off",
  "vitest/prefer-to-be-truthy": "off",
  "vitest/require-local-test-context-for-concurrent-snapshots": "off",
  "vitest/consistent-test-it": "off",
  "vitest/expect-expect": "off",
  "vitest/max-expects": "off",
  "vitest/max-nested-describe": "off",
  "vitest/no-alias-methods": "off",
  "vitest/no-commented-out-tests": "off",
  "vitest/no-conditional-expect": "off",
  "vitest/no-conditional-in-test": "off",
  "vitest/no-disabled-tests": "off",
  "vitest/no-duplicate-hooks": "off",
  "vitest/no-focused-tests": "off",
  "vitest/no-hooks": "off",
  "vitest/no-identical-title": "off",
  "vitest/no-interpolation-in-snapshots": "off",
  "vitest/no-restricted-jest-methods": "off",
  "vitest/no-restricted-matchers": "off",
  "vitest/no-standalone-expect": "off",
  "vitest/no-test-prefixes": "off",
  "vitest/no-test-return-statement": "off",
  "vitest/prefer-each": "off",
  "vitest/prefer-comparison-matcher": "off",
  "vitest/prefer-equality-matcher": "off",
  "vitest/prefer-expect-resolves": "off",
  "vitest/prefer-hooks-in-order": "off",
  "vitest/prefer-hooks-on-top": "off",
  "vitest/prefer-lowercase-title": "off",
  "vitest/prefer-mock-promise-shorthand": "off",
  "vitest/prefer-strict-equal": "off",
  "vitest/prefer-to-have-length": "off",
  "vitest/prefer-todo": "off",
  "vitest/require-to-throw-message": "off",
  "vitest/require-top-level-describe": "off",
  "vitest/valid-describe-callback": "off",
  "vitest/valid-expect": "off"
};
export {
  eslintRules,
  importRules,
  jestRules,
  jsdocRules,
  jsxA11yRules,
  nextjsRules,
  nodeRules,
  promiseRules,
  reactHooksRules,
  reactPerfRules,
  reactRules,
  typescriptRules,
  unicornRules,
  vitestRules
};
