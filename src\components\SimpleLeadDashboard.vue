<template>
  <div class="overflow-hidden bg-gray-100">
    <!-- Header -->
    <DashboardHeader />

    <div class="w-full max-md:max-w-full">
      <div class="flex gap-5 max-md:flex-col">
        <!-- Sidebar -->
        <DashboardSidebar />

        <!-- Main Content -->
        <main class="ml-5 w-[83%] max-md:ml-0 max-md:w-full">
          <div class="self-stretch my-auto w-full max-md:mt-10 max-md:max-w-full">
            <!-- Add DashboardTabs -->
            <DashboardTabs />

            <!-- Add WelcomeSection -->
            <WelcomeSection />

            <!-- Add TopLeadProviders -->
            <TopLeadProviders />

            <!-- Add StatsGrid -->
            <StatsGrid />

            <!-- Add TopLeadsComparison -->
            <TopLeadsComparison />

            <!-- Add LeadStatusTable -->
            <LeadStatusTable />
          </div>
        </main>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import DashboardHeader from "./dashboard-lead-analysis/DashboardHeader.vue";
import DashboardSidebar from "./dashboard-lead-analysis/DashboardSidebar.vue";
import DashboardTabs from "./dashboard-lead-analysis/DashboardTabs.vue";
import WelcomeSection from "./dashboard-lead-analysis/WelcomeSection.vue";
import TopLeadProviders from "./dashboard-lead-analysis/TopLeadProviders.vue";
import StatsGrid from "./dashboard-lead-analysis/StatsGrid.vue";
import TopLeadsComparison from "./dashboard-lead-analysis/TopLeadsComparison.vue";
import LeadStatusTable from "./dashboard-lead-analysis/LeadStatusTable.vue";
</script>
