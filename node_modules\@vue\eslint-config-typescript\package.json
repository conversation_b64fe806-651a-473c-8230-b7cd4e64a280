{"name": "@vue/eslint-config-typescript", "version": "14.5.1", "description": "ESLint config for TypeScript + Vue.js projects", "main": "./dist/index.mjs", "type": "module", "files": ["dist"], "exports": {"require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "import": {"types": "./dist/index.d.mts", "default": "./dist/index.mjs"}}, "publishConfig": {"access": "public", "provenance": true}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/eslint-config-typescript.git"}, "keywords": ["vue", "create-vue", "create-eslint-config", "eslint", "typescript"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/eslint-config-typescript/issues"}, "homepage": "https://github.com/vuejs/eslint-config-typescript#readme", "devDependencies": {"@tsconfig/node20": "^20.1.6", "@types/node": "^22.15.32", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "execa": "^9.6.0", "pkgroll": "^2.13.1", "prettier": "3.5.3", "tsx": "^4.20.3", "typescript": "~5.8.2", "vitest": "^3.2.3", "vue": "^3.5.16"}, "peerDependencies": {"eslint": "^9.10.0", "eslint-plugin-vue": "^9.28.0 || ^10.0.0", "typescript": ">=4.8.4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "dependencies": {"@typescript-eslint/utils": "^8.34.1", "fast-glob": "^3.3.3", "typescript-eslint": "^8.34.1", "vue-eslint-parser": "^10.1.3"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "scripts": {"dev": "pkgroll --watch", "build": "pkgroll", "format": "prettier --write src", "type-check": "tsc --noEmit", "test": "pkgroll && vitest --dir test --testTimeout 30000"}}