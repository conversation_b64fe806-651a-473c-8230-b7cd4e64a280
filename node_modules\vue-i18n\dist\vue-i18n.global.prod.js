/*!
  * vue-i18n v9.14.4
  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>
  * Released under the MIT License.
  */
var VueI18n=function(e,t){"use strict";const n="undefined"!=typeof window,r=(e,t=!1)=>t?Symbol.for(e):Symbol(e),a=(e,t,n)=>o({l:e,k:t,s:n}),o=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),l=e=>"number"==typeof e&&isFinite(e),s=e=>"[object Date]"===N(e),c=e=>"[object RegExp]"===N(e),u=e=>T(e)&&0===Object.keys(e).length,i=Object.assign,f=Object.create,m=(e=null)=>f(e);function _(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const p=Object.prototype.hasOwnProperty;function d(e,t){return p.call(e,t)}const g=Array.isArray,E=e=>"function"==typeof e,b=e=>"string"==typeof e,v=e=>"boolean"==typeof e,k=e=>null!==e&&"object"==typeof e,h=e=>k(e)&&E(e.then)&&E(e.catch),L=Object.prototype.toString,N=e=>L.call(e),T=e=>{if(!k(e))return!1;const t=Object.getPrototypeOf(e);return null===t||t.constructor===Object};function y(e,t=""){return e.reduce(((e,n,r)=>0===r?e+n:e+t+n),"")}function I(e){let t=e;return()=>++t}function O(e,t){"undefined"!=typeof console&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const C=e=>!k(e)||g(e);function P(e,t){if(C(e)||C(t))throw new Error("Invalid value");const n=[{src:e,des:t}];for(;n.length;){const{src:e,des:t}=n.pop();Object.keys(e).forEach((r=>{"__proto__"!==r&&(k(e[r])&&!k(t[r])&&(t[r]=Array.isArray(e[r])?[]:m()),C(t[r])||C(e[r])?t[r]=e[r]:n.push({src:e[r],des:t[r]}))}))}}function A(e,t,n){const r={start:e,end:t};return null!=n&&(r.source=n),r}const R={USE_MODULO_SYNTAX:1,__EXTEND_POINT__:2};const F={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16,__EXTEND_POINT__:17};function S(e,t,n={}){const{domain:r,messages:a,args:o}=n,l=new SyntaxError(String(e));return l.code=e,t&&(l.location=t),l.domain=r,l}function D(e){throw e}const M=" ",w="\r",x="\n",W=String.fromCharCode(8232),U=String.fromCharCode(8233);function $(e){const t=e;let n=0,r=1,a=1,o=0;const l=e=>t[e]===w&&t[e+1]===x,s=e=>t[e]===U,c=e=>t[e]===W,u=e=>l(e)||(e=>t[e]===x)(e)||s(e)||c(e),i=e=>l(e)||s(e)||c(e)?x:t[e];function f(){return o=0,u(n)&&(r++,a=0),l(n)&&n++,n++,a++,t[n]}return{index:()=>n,line:()=>r,column:()=>a,peekOffset:()=>o,charAt:i,currentChar:()=>i(n),currentPeek:()=>i(n+o),next:f,peek:function(){return l(n+o)&&o++,o++,t[n+o]},reset:function(){n=0,r=1,a=1,o=0},resetPeek:function(e=0){o=e},skipToPeek:function(){const e=n+o;for(;e!==n;)f();o=0}}}const H=void 0,V="'";function j(e,t={}){const n=!1!==t.location,r=$(e),a=()=>r.index(),o=()=>{return e=r.line(),t=r.column(),n=r.index(),{line:e,column:t,offset:n};var e,t,n},l=o(),s=a(),c={currentType:14,offset:s,startLoc:l,endLoc:l,lastType:14,lastOffset:s,lastStartLoc:l,lastEndLoc:l,braceNest:0,inLinked:!1,text:""},u=()=>c,{onError:i}=t;function f(e,t,r){e.endLoc=o(),e.currentType=t;const a={type:t};return n&&(a.loc=A(e.startLoc,e.endLoc)),null!=r&&(a.value=r),a}const m=e=>f(e,14);function _(e,t){return e.currentChar()===t?(e.next(),t):(F.EXPECTED_TOKEN,o(),"")}function p(e){let t="";for(;e.currentPeek()===M||e.currentPeek()===x;)t+=e.currentPeek(),e.peek();return t}function d(e){const t=p(e);return e.skipToPeek(),t}function g(e){if(e===H)return!1;const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||95===t}function E(e,t){const{currentType:n}=t;if(2!==n)return!1;p(e);const r=function(e){if(e===H)return!1;const t=e.charCodeAt(0);return t>=48&&t<=57}("-"===e.currentPeek()?e.peek():e.currentPeek());return e.resetPeek(),r}function b(e){p(e);const t="|"===e.currentPeek();return e.resetPeek(),t}function v(e,t=!0){const n=(t=!1,r="",a=!1)=>{const o=e.currentPeek();return"{"===o?"%"!==r&&t:"@"!==o&&o?"%"===o?(e.peek(),n(t,"%",!0)):"|"===o?!("%"!==r&&!a)||!(r===M||r===x):o===M?(e.peek(),n(!0,M,a)):o!==x||(e.peek(),n(!0,x,a)):"%"===r||t},r=n();return t&&e.resetPeek(),r}function k(e,t){const n=e.currentChar();return n===H?H:t(n)?(e.next(),n):null}function h(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t}function L(e){return k(e,h)}function N(e){const t=e.charCodeAt(0);return t>=97&&t<=122||t>=65&&t<=90||t>=48&&t<=57||95===t||36===t||45===t}function T(e){return k(e,N)}function y(e){const t=e.charCodeAt(0);return t>=48&&t<=57}function I(e){return k(e,y)}function O(e){const t=e.charCodeAt(0);return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function C(e){return k(e,O)}function P(e){let t="",n="";for(;t=I(e);)n+=t;return n}function R(e){let t="";for(;;){const n=e.currentChar();if("{"===n||"}"===n||"@"===n||"|"===n||!n)break;if("%"===n){if(!v(e))break;t+=n,e.next()}else if(n===M||n===x)if(v(e))t+=n,e.next();else{if(b(e))break;t+=n,e.next()}else t+=n,e.next()}return t}function S(e){return e!==V&&e!==x}function D(e){const t=e.currentChar();switch(t){case"\\":case"'":return e.next(),`\\${t}`;case"u":return w(e,t,4);case"U":return w(e,t,6);default:return F.UNKNOWN_ESCAPE_SEQUENCE,o(),""}}function w(e,t,n){_(e,t);let r="";for(let a=0;a<n;a++){const t=C(e);if(!t){F.INVALID_UNICODE_ESCAPE_SEQUENCE,o(),e.currentChar();break}r+=t}return`\\${t}${r}`}function W(e){return"{"!==e&&"}"!==e&&e!==M&&e!==x}function U(e){d(e);const t=_(e,"|");return d(e),t}function j(e,t){let n=null;switch(e.currentChar()){case"{":return t.braceNest>=1&&(F.NOT_ALLOW_NEST_PLACEHOLDER,o()),e.next(),n=f(t,2,"{"),d(e),t.braceNest++,n;case"}":return t.braceNest>0&&2===t.currentType&&(F.EMPTY_PLACEHOLDER,o()),e.next(),n=f(t,3,"}"),t.braceNest--,t.braceNest>0&&d(e),t.inLinked&&0===t.braceNest&&(t.inLinked=!1),n;case"@":return t.braceNest>0&&(F.UNTERMINATED_CLOSING_BRACE,o()),n=X(e,t)||m(t),t.braceNest=0,n;default:{let r=!0,a=!0,l=!0;if(b(e))return t.braceNest>0&&(F.UNTERMINATED_CLOSING_BRACE,o()),n=f(t,1,U(e)),t.braceNest=0,t.inLinked=!1,n;if(t.braceNest>0&&(5===t.currentType||6===t.currentType||7===t.currentType))return F.UNTERMINATED_CLOSING_BRACE,o(),t.braceNest=0,G(e,t);if(r=function(e,t){const{currentType:n}=t;if(2!==n)return!1;p(e);const r=g(e.currentPeek());return e.resetPeek(),r}(e,t))return n=f(t,5,function(e){d(e);let t="",n="";for(;t=T(e);)n+=t;return e.currentChar()===H&&(F.UNTERMINATED_CLOSING_BRACE,o()),n}(e)),d(e),n;if(a=E(e,t))return n=f(t,6,function(e){d(e);let t="";return"-"===e.currentChar()?(e.next(),t+=`-${P(e)}`):t+=P(e),e.currentChar()===H&&(F.UNTERMINATED_CLOSING_BRACE,o()),t}(e)),d(e),n;if(l=function(e,t){const{currentType:n}=t;if(2!==n)return!1;p(e);const r=e.currentPeek()===V;return e.resetPeek(),r}(e,t))return n=f(t,7,function(e){d(e),_(e,"'");let t="",n="";for(;t=k(e,S);)n+="\\"===t?D(e):t;const r=e.currentChar();return r===x||r===H?(F.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,o(),r===x&&(e.next(),_(e,"'")),n):(_(e,"'"),n)}(e)),d(e),n;if(!r&&!a&&!l)return n=f(t,13,function(e){d(e);let t="",n="";for(;t=k(e,W);)n+=t;return n}(e)),F.INVALID_TOKEN_IN_PLACEHOLDER,o(),n.value,d(e),n;break}}return n}function X(e,t){const{currentType:n}=t;let r=null;const a=e.currentChar();switch(8!==n&&9!==n&&12!==n&&10!==n||a!==x&&a!==M||(F.INVALID_LINKED_FORMAT,o()),a){case"@":return e.next(),r=f(t,8,"@"),t.inLinked=!0,r;case".":return d(e),e.next(),f(t,9,".");case":":return d(e),e.next(),f(t,10,":");default:return b(e)?(r=f(t,1,U(e)),t.braceNest=0,t.inLinked=!1,r):function(e,t){const{currentType:n}=t;if(8!==n)return!1;p(e);const r="."===e.currentPeek();return e.resetPeek(),r}(e,t)||function(e,t){const{currentType:n}=t;if(8!==n&&12!==n)return!1;p(e);const r=":"===e.currentPeek();return e.resetPeek(),r}(e,t)?(d(e),X(e,t)):function(e,t){const{currentType:n}=t;if(9!==n)return!1;p(e);const r=g(e.currentPeek());return e.resetPeek(),r}(e,t)?(d(e),f(t,12,function(e){let t="",n="";for(;t=L(e);)n+=t;return n}(e))):function(e,t){const{currentType:n}=t;if(10!==n)return!1;const r=()=>{const t=e.currentPeek();return"{"===t?g(e.peek()):!("@"===t||"%"===t||"|"===t||":"===t||"."===t||t===M||!t)&&(t===x?(e.peek(),r()):v(e,!1))},a=r();return e.resetPeek(),a}(e,t)?(d(e),"{"===a?j(e,t)||r:f(t,11,function(e){const t=n=>{const r=e.currentChar();return"{"!==r&&"%"!==r&&"@"!==r&&"|"!==r&&"("!==r&&")"!==r&&r?r===M?n:(n+=r,e.next(),t(n)):n};return t("")}(e))):(8===n&&(F.INVALID_LINKED_FORMAT,o()),t.braceNest=0,t.inLinked=!1,G(e,t))}}function G(e,t){let n={type:14};if(t.braceNest>0)return j(e,t)||m(t);if(t.inLinked)return X(e,t)||m(t);switch(e.currentChar()){case"{":return j(e,t)||m(t);case"}":return F.UNBALANCED_CLOSING_BRACE,o(),e.next(),f(t,3,"}");case"@":return X(e,t)||m(t);default:{if(b(e))return n=f(t,1,U(e)),t.braceNest=0,t.inLinked=!1,n;const{isModulo:r,hasSpace:a}=function(e){const t=p(e),n="%"===e.currentPeek()&&"{"===e.peek();return e.resetPeek(),{isModulo:n,hasSpace:t.length>0}}(e);if(r)return a?f(t,0,R(e)):f(t,4,function(e){d(e);const t=e.currentChar();return"%"!==t&&(F.EXPECTED_TOKEN,o()),e.next(),"%"}(e));if(v(e))return f(t,0,R(e));break}}return n}return{nextToken:function(){const{currentType:e,offset:t,startLoc:n,endLoc:l}=c;return c.lastType=e,c.lastOffset=t,c.lastStartLoc=n,c.lastEndLoc=l,c.offset=a(),c.startLoc=o(),r.currentChar()===H?f(c,14):G(r,c)},currentOffset:a,currentPosition:o,context:u}}const X=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function G(e,t,n){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const e=parseInt(t||n,16);return e<=55295||e>=57344?String.fromCodePoint(e):"�"}}}function Y(e={}){const t=!1!==e.location,{onError:n,onWarn:r}=e;function a(e,n,r){const a={type:e};return t&&(a.start=n,a.end=n,a.loc={start:r,end:r}),a}function o(e,n,r,a){a&&(e.type=a),t&&(e.end=n,e.loc&&(e.loc.end=r))}function l(e,t){const n=e.context(),r=a(3,n.offset,n.startLoc);return r.value=t,o(r,e.currentOffset(),e.currentPosition()),r}function s(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:l}=n,s=a(5,r,l);return s.index=parseInt(t,10),e.nextToken(),o(s,e.currentOffset(),e.currentPosition()),s}function c(e,t,n){const r=e.context(),{lastOffset:l,lastStartLoc:s}=r,c=a(4,l,s);return c.key=t,!0===n&&(c.modulo=!0),e.nextToken(),o(c,e.currentOffset(),e.currentPosition()),c}function u(e,t){const n=e.context(),{lastOffset:r,lastStartLoc:l}=n,s=a(9,r,l);return s.value=t.replace(X,G),e.nextToken(),o(s,e.currentOffset(),e.currentPosition()),s}function f(e){const t=e.context(),n=a(6,t.offset,t.startLoc);let r=e.nextToken();if(9===r.type){const t=function(e){const t=e.nextToken(),n=e.context(),{lastOffset:r,lastStartLoc:l}=n,s=a(8,r,l);return 12!==t.type?(F.UNEXPECTED_EMPTY_LINKED_MODIFIER,n.lastStartLoc,s.value="",o(s,r,l),{nextConsumeToken:t,node:s}):(null==t.value&&(F.UNEXPECTED_LEXICAL_ANALYSIS,n.lastStartLoc,B(t)),s.value=t.value||"",o(s,e.currentOffset(),e.currentPosition()),{node:s})}(e);n.modifier=t.node,r=t.nextConsumeToken||e.nextToken()}switch(10!==r.type&&(F.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,B(r)),r=e.nextToken(),2===r.type&&(r=e.nextToken()),r.type){case 11:null==r.value&&(F.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,B(r)),n.key=function(e,t){const n=e.context(),r=a(7,n.offset,n.startLoc);return r.value=t,o(r,e.currentOffset(),e.currentPosition()),r}(e,r.value||"");break;case 5:null==r.value&&(F.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,B(r)),n.key=c(e,r.value||"");break;case 6:null==r.value&&(F.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,B(r)),n.key=s(e,r.value||"");break;case 7:null==r.value&&(F.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,B(r)),n.key=u(e,r.value||"");break;default:{F.UNEXPECTED_EMPTY_LINKED_KEY,t.lastStartLoc;const l=e.context(),s=a(7,l.offset,l.startLoc);return s.value="",o(s,l.offset,l.startLoc),n.key=s,o(n,l.offset,l.startLoc),{nextConsumeToken:r,node:n}}}return o(n,e.currentOffset(),e.currentPosition()),{node:n}}function m(e){const t=e.context(),n=a(2,1===t.currentType?e.currentOffset():t.offset,1===t.currentType?t.endLoc:t.startLoc);n.items=[];let r=null,i=null;do{const a=r||e.nextToken();switch(r=null,a.type){case 0:null==a.value&&(F.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,B(a)),n.items.push(l(e,a.value||""));break;case 6:null==a.value&&(F.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,B(a)),n.items.push(s(e,a.value||""));break;case 4:i=!0;break;case 5:null==a.value&&(F.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,B(a)),n.items.push(c(e,a.value||"",!!i)),i&&(R.USE_MODULO_SYNTAX,t.lastStartLoc,B(a),i=null);break;case 7:null==a.value&&(F.UNEXPECTED_LEXICAL_ANALYSIS,t.lastStartLoc,B(a)),n.items.push(u(e,a.value||""));break;case 8:{const t=f(e);n.items.push(t.node),r=t.nextConsumeToken||null;break}}}while(14!==t.currentType&&1!==t.currentType);return o(n,1===t.currentType?t.lastOffset:e.currentOffset(),1===t.currentType?t.lastEndLoc:e.currentPosition()),n}function _(e){const t=e.context(),{offset:n,startLoc:r}=t,l=m(e);return 14===t.currentType?l:function(e,t,n,r){const l=e.context();let s=0===r.items.length;const c=a(1,t,n);c.cases=[],c.cases.push(r);do{const t=m(e);s||(s=0===t.items.length),c.cases.push(t)}while(14!==l.currentType);return o(c,e.currentOffset(),e.currentPosition()),c}(e,n,r,l)}return{parse:function(n){const r=j(n,i({},e)),l=r.context(),s=a(0,l.offset,l.startLoc);return t&&s.loc&&(s.loc.source=n),s.body=_(r),e.onCacheKey&&(s.cacheKey=e.onCacheKey(n)),14!==l.currentType&&(F.UNEXPECTED_LEXICAL_ANALYSIS,l.lastStartLoc,n[l.offset]),o(s,r.currentOffset(),r.currentPosition()),s}}}function B(e){if(14===e.type)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function K(e,t){for(let n=0;n<e.length;n++)z(e[n],t)}function z(e,t){switch(e.type){case 1:K(e.cases,t),t.helper("plural");break;case 2:K(e.items,t);break;case 6:z(e.key,t),t.helper("linked"),t.helper("type");break;case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named")}}function J(e,t={}){const n=function(e,t={}){const n={ast:e,helpers:new Set};return{context:()=>n,helper:e=>(n.helpers.add(e),e)}}(e);n.helper("normalize"),e.body&&z(e.body,n);const r=n.context();e.helpers=Array.from(r.helpers)}function Q(e){if(1===e.items.length){const t=e.items[0];3!==t.type&&9!==t.type||(e.static=t.value,delete t.value)}else{const t=[];for(let n=0;n<e.items.length;n++){const r=e.items[n];if(3!==r.type&&9!==r.type)break;if(null==r.value)break;t.push(r.value)}if(t.length===e.items.length){e.static=y(t);for(let t=0;t<e.items.length;t++){const n=e.items[t];3!==n.type&&9!==n.type||delete n.value}}}}function q(e){switch(e.t=e.type,e.type){case 0:{const t=e;q(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,n=t.cases;for(let e=0;e<n.length;e++)q(n[e]);t.c=n,delete t.cases;break}case 2:{const t=e,n=t.items;for(let e=0;e<n.length;e++)q(n[e]);t.i=n,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;q(t.key),t.k=t.key,delete t.key,t.modifier&&(q(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function Z(e,t){const{helper:n}=e;switch(t.type){case 0:!function(e,t){t.body?Z(e,t.body):e.push("null")}(e,t);break;case 1:!function(e,t){const{helper:n,needIndent:r}=e;if(t.cases.length>1){e.push(`${n("plural")}([`),e.indent(r());const a=t.cases.length;for(let n=0;n<a&&(Z(e,t.cases[n]),n!==a-1);n++)e.push(", ");e.deindent(r()),e.push("])")}}(e,t);break;case 2:!function(e,t){const{helper:n,needIndent:r}=e;e.push(`${n("normalize")}([`),e.indent(r());const a=t.items.length;for(let o=0;o<a&&(Z(e,t.items[o]),o!==a-1);o++)e.push(", ");e.deindent(r()),e.push("])")}(e,t);break;case 6:!function(e,t){const{helper:n}=e;e.push(`${n("linked")}(`),Z(e,t.key),t.modifier?(e.push(", "),Z(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}(e,t);break;case 8:case 7:case 9:case 3:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${n("interpolate")}(${n("list")}(${t.index}))`,t);break;case 4:e.push(`${n("interpolate")}(${n("named")}(${JSON.stringify(t.key)}))`,t)}}const ee=(e,t={})=>{const n=b(t.mode)?t.mode:"normal",r=b(t.filename)?t.filename:"message.intl",a=!!t.sourceMap,o=null!=t.breakLineCode?t.breakLineCode:"arrow"===n?";":"\n",l=t.needIndent?t.needIndent:"arrow"!==n,s=e.helpers||[],c=function(e,t){const{sourceMap:n,filename:r,breakLineCode:a,needIndent:o}=t,l=!1!==t.location,s={filename:r,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:a,needIndent:o,indentLevel:0};function c(e,t){s.code+=e}function u(e,t=!0){const n=t?a:"";c(o?n+"  ".repeat(e):n)}return l&&e.loc&&(s.source=e.loc.source),{context:()=>s,push:c,indent:function(e=!0){const t=++s.indentLevel;e&&u(t)},deindent:function(e=!0){const t=--s.indentLevel;e&&u(t)},newline:function(){u(s.indentLevel)},helper:e=>`_${e}`,needIndent:()=>s.needIndent}}(e,{mode:n,filename:r,sourceMap:a,breakLineCode:o,needIndent:l});c.push("normal"===n?"function __msg__ (ctx) {":"(ctx) => {"),c.indent(l),s.length>0&&(c.push(`const { ${y(s.map((e=>`${e}: _${e}`)),", ")} } = ctx`),c.newline()),c.push("return "),Z(c,e),c.deindent(l),c.push("}"),delete e.helpers;const{code:u,map:i}=c.context();return{ast:e,code:u,map:i?i.toJSON():void 0}};function te(e,t={}){const n=i({},t),r=!!n.jit,a=!!n.minify,o=null==n.optimize||n.optimize,l=Y(n).parse(e);return r?(o&&function(e){const t=e.body;2===t.type?Q(t):t.cases.forEach((e=>Q(e)))}(l),a&&q(l),{ast:l,code:""}):(J(l,n),ee(l,n))}function ne(e){return k(e)&&0===ce(e)&&(d(e,"b")||d(e,"body"))}const re=["b","body"];const ae=["c","cases"];const oe=["s","static"];const le=["i","items"];const se=["t","type"];function ce(e){return _e(e,se)}const ue=["v","value"];function ie(e,t){const n=_e(e,ue);if(null!=n)return n;throw de(t)}const fe=["m","modifier"];const me=["k","key"];function _e(e,t,n){for(let r=0;r<t.length;r++){const n=t[r];if(d(e,n)&&null!=e[n])return e[n]}return n}const pe=[...re,...ae,...oe,...le,...me,...fe,...ue,...se];function de(e){return new Error(`unhandled node type: ${e}`)}const ge=[];ge[0]={w:[0],i:[3,0],"[":[4],o:[7]},ge[1]={w:[1],".":[2],"[":[4],o:[7]},ge[2]={w:[2],i:[3,0],0:[3,0]},ge[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]},ge[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]},ge[5]={"'":[4,0],o:8,l:[5,0]},ge[6]={'"':[4,0],o:8,l:[6,0]};const Ee=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function be(e){if(null==e)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function ve(e){const t=e.trim();return("0"!==e.charAt(0)||!isNaN(parseInt(e)))&&(n=t,Ee.test(n)?function(e){const t=e.charCodeAt(0);return t!==e.charCodeAt(e.length-1)||34!==t&&39!==t?e:e.slice(1,-1)}(t):"*"+t);var n}const ke=new Map;function he(e,t){return k(e)?e[t]:null}const Le=e=>e,Ne=e=>"",Te="text",ye=e=>0===e.length?"":y(e),Ie=e=>null==e?"":g(e)||T(e)&&e.toString===L?JSON.stringify(e,null,2):String(e);function Oe(e,t){return e=Math.abs(e),2===t?e?e>1?1:0:1:e?Math.min(e,2):0}function Ce(e={}){const t=e.locale,n=function(e){const t=l(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(l(e.named.count)||l(e.named.n))?l(e.named.count)?e.named.count:l(e.named.n)?e.named.n:t:t}(e),r=k(e.pluralRules)&&b(t)&&E(e.pluralRules[t])?e.pluralRules[t]:Oe,a=k(e.pluralRules)&&b(t)&&E(e.pluralRules[t])?Oe:void 0,o=e.list||[],s=e.named||m();l(e.pluralIndex)&&function(e,t){t.count||(t.count=e),t.n||(t.n=e)}(n,s);function c(t){const n=E(e.messages)?e.messages(t):!!k(e.messages)&&e.messages[t];return n||(e.parent?e.parent.message(t):Ne)}const u=T(e.processor)&&E(e.processor.normalize)?e.processor.normalize:ye,f=T(e.processor)&&E(e.processor.interpolate)?e.processor.interpolate:Ie,_={list:e=>o[e],named:e=>s[e],plural:e=>e[r(n,e.length,a)],linked:(t,...n)=>{const[r,a]=n;let o="text",l="";1===n.length?k(r)?(l=r.modifier||l,o=r.type||o):b(r)&&(l=r||l):2===n.length&&(b(r)&&(l=r||l),b(a)&&(o=a||o));const s=c(t)(_),u="vnode"===o&&g(s)&&l?s[0]:s;return l?(i=l,e.modifiers?e.modifiers[i]:Le)(u,o):u;var i},message:c,type:T(e.processor)&&b(e.processor.type)?e.processor.type:Te,interpolate:f,normalize:u,values:i(m(),o,s)};return _}const Pe=F.__EXTEND_POINT__,Ae=I(Pe),Re={INVALID_ARGUMENT:Pe,INVALID_DATE_ARGUMENT:Ae(),INVALID_ISO_DATE_ARGUMENT:Ae(),NOT_SUPPORT_NON_STRING_MESSAGE:Ae(),NOT_SUPPORT_LOCALE_PROMISE_VALUE:Ae(),NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:Ae(),NOT_SUPPORT_LOCALE_TYPE:Ae(),__EXTEND_POINT__:Ae()};function Fe(e,t){return null!=t.locale?De(t.locale):De(e.locale)}let Se;function De(e){if(b(e))return e;if(E(e)){if(e.resolvedOnce&&null!=Se)return Se;if("Function"===e.constructor.name){const t=e();if(h(t))throw Error(Re.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return Se=t}throw Error(Re.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}throw Error(Re.NOT_SUPPORT_LOCALE_TYPE)}function Me(e,t,n){return[...new Set([n,...g(t)?t:k(t)?Object.keys(t):b(t)?[t]:[n]])]}function we(e,t,n){const r=b(n)?n:Ve,a=e;a.__localeChainCache||(a.__localeChainCache=new Map);let o=a.__localeChainCache.get(r);if(!o){o=[];let e=[n];for(;g(e);)e=xe(o,e,t);const l=g(t)||!T(t)?t:t.default?t.default:null;e=b(l)?[l]:l,g(e)&&xe(o,e,!1),a.__localeChainCache.set(r,o)}return o}function xe(e,t,n){let r=!0;for(let a=0;a<t.length&&v(r);a++){const o=t[a];b(o)&&(r=We(e,t[a],n))}return r}function We(e,t,n){let r;const a=t.split("-");do{r=Ue(e,a.join("-"),n),a.splice(-1,1)}while(a.length&&!0===r);return r}function Ue(e,t,n){let r=!1;if(!e.includes(t)&&(r=!0,t)){r="!"!==t[t.length-1];const a=t.replace(/!/g,"");e.push(a),(g(n)||T(n))&&n[a]&&(r=n[a])}return r}const $e="9.14.4",He=-1,Ve="en-US",je="",Xe=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;let Ge,Ye,Be;let Ke=null;const ze=e=>{Ke=e},Je=()=>Ke;let Qe=0;function qe(e={}){const t=E(e.onWarn)?e.onWarn:O,n=b(e.version)?e.version:$e,r=b(e.locale)||E(e.locale)?e.locale:Ve,a=E(r)?Ve:r,o=g(e.fallbackLocale)||T(e.fallbackLocale)||b(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:a,l=T(e.messages)?e.messages:Ze(a),s=T(e.datetimeFormats)?e.datetimeFormats:Ze(a),u=T(e.numberFormats)?e.numberFormats:Ze(a),f=i(m(),e.modifiers,{upper:(e,t)=>"text"===t&&b(e)?e.toUpperCase():"vnode"===t&&k(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>"text"===t&&b(e)?e.toLowerCase():"vnode"===t&&k(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>"text"===t&&b(e)?Xe(e):"vnode"===t&&k(e)&&"__v_isVNode"in e?Xe(e.children):e}),_=e.pluralRules||m(),p=E(e.missing)?e.missing:null,d=!v(e.missingWarn)&&!c(e.missingWarn)||e.missingWarn,h=!v(e.fallbackWarn)&&!c(e.fallbackWarn)||e.fallbackWarn,L=!!e.fallbackFormat,N=!!e.unresolving,y=E(e.postTranslation)?e.postTranslation:null,I=T(e.processor)?e.processor:null,C=!v(e.warnHtmlMessage)||e.warnHtmlMessage,P=!!e.escapeParameter,A=E(e.messageCompiler)?e.messageCompiler:Ge,R=E(e.messageResolver)?e.messageResolver:Ye||he,F=E(e.localeFallbacker)?e.localeFallbacker:Be||Me,S=k(e.fallbackContext)?e.fallbackContext:void 0,D=e,M=k(D.__datetimeFormatters)?D.__datetimeFormatters:new Map,w=k(D.__numberFormatters)?D.__numberFormatters:new Map,x=k(D.__meta)?D.__meta:{};Qe++;const W={version:n,cid:Qe,locale:r,fallbackLocale:o,messages:l,modifiers:f,pluralRules:_,missing:p,missingWarn:d,fallbackWarn:h,fallbackFormat:L,unresolving:N,postTranslation:y,processor:I,warnHtmlMessage:C,escapeParameter:P,messageCompiler:A,messageResolver:R,localeFallbacker:F,fallbackContext:S,onWarn:t,__meta:x};return W.datetimeFormats=s,W.numberFormats=u,W.__datetimeFormatters=M,W.__numberFormatters=w,W}const Ze=e=>({[e]:m()});function et(e,t,n,r,a){const{missing:o,onWarn:l}=e;if(null!==o){const r=o(e,n,t,a);return b(r)?r:t}return t}function tt(e,t,n){e.__localeChainCache=new Map,e.localeFallbacker(e,n,t)}function nt(e,t){const n=t.indexOf(e);if(-1===n)return!1;for(let o=n+1;o<t.length;o++)if(r=e,a=t[o],r!==a&&r.split("-")[0]===a.split("-")[0])return!0;var r,a;return!1}function rt(e){return t=>function(e,t){const n=(r=t,_e(r,re));var r;if(null==n)throw de(0);if(1===ce(n)){const t=function(e){return _e(e,ae,[])}(n);return e.plural(t.reduce(((t,n)=>[...t,at(e,n)]),[]))}return at(e,n)}(t,e)}function at(e,t){const n=function(e){return _e(e,oe)}(t);if(null!=n)return"text"===e.type?n:e.normalize([n]);{const n=function(e){return _e(e,le,[])}(t).reduce(((t,n)=>[...t,ot(e,n)]),[]);return e.normalize(n)}}function ot(e,t){const n=ce(t);switch(n){case 3:case 9:case 7:case 8:return ie(t,n);case 4:{const r=t;if(d(r,"k")&&r.k)return e.interpolate(e.named(r.k));if(d(r,"key")&&r.key)return e.interpolate(e.named(r.key));throw de(n)}case 5:{const r=t;if(d(r,"i")&&l(r.i))return e.interpolate(e.list(r.i));if(d(r,"index")&&l(r.index))return e.interpolate(e.list(r.index));throw de(n)}case 6:{const n=t,r=function(e){return _e(e,fe)}(n),a=function(e){const t=_e(e,me);if(t)return t;throw de(6)}(n);return e.linked(ot(e,a),r?ot(e,r):void 0,e.type)}default:throw new Error(`unhandled node on format message part: ${n}`)}}const lt=e=>e;let st=m();const ct=()=>"",ut=e=>E(e);function it(e,...t){const{fallbackFormat:n,postTranslation:r,unresolving:a,messageCompiler:o,fallbackLocale:s,messages:c}=e,[u,i]=_t(...t),f=v(i.missingWarn)?i.missingWarn:e.missingWarn,p=v(i.fallbackWarn)?i.fallbackWarn:e.fallbackWarn,d=v(i.escapeParameter)?i.escapeParameter:e.escapeParameter,E=!!i.resolvedMessage,h=b(i.default)||v(i.default)?v(i.default)?o?u:()=>u:i.default:n?o?u:()=>u:"",L=n||""!==h,N=Fe(e,i);d&&function(e){g(e.list)?e.list=e.list.map((e=>b(e)?_(e):e)):k(e.named)&&Object.keys(e.named).forEach((t=>{b(e.named[t])&&(e.named[t]=_(e.named[t]))}))}(i);let[T,y,I]=E?[u,N,c[N]||m()]:ft(e,u,N,s,p,f),O=T,C=u;if(E||b(O)||ne(O)||ut(O)||L&&(O=h,C=O),!(E||(b(O)||ne(O)||ut(O))&&b(y)))return a?He:u;let P=!1;const A=ut(O)?O:mt(e,u,y,O,C,(()=>{P=!0}));if(P)return O;const R=function(e,t,n,r){const{modifiers:a,pluralRules:o,messageResolver:s,fallbackLocale:c,fallbackWarn:u,missingWarn:i,fallbackContext:f}=e,m=r=>{let a=s(n,r);if(null==a&&f){const[,,e]=ft(f,r,t,c,u,i);a=s(e,r)}if(b(a)||ne(a)){let n=!1;const o=mt(e,r,t,a,r,(()=>{n=!0}));return n?ct:o}return ut(a)?a:ct},_={locale:t,modifiers:a,pluralRules:o,messages:m};e.processor&&(_.processor=e.processor);r.list&&(_.list=r.list);r.named&&(_.named=r.named);l(r.plural)&&(_.pluralIndex=r.plural);return _}(e,y,I,i),F=function(e,t,n){const r=t(n);return r}(0,A,Ce(R));return r?r(F,u):F}function ft(e,t,n,r,a,o){const{messages:l,onWarn:s,messageResolver:c,localeFallbacker:u}=e,i=u(e,r,n);let f,_=m(),p=null;for(let d=0;d<i.length&&(f=i[d],_=l[f]||m(),null===(p=c(_,t))&&(p=_[t]),!(b(p)||ne(p)||ut(p)));d++)if(!nt(f,i)){const n=et(e,t,f,0,"translate");n!==t&&(p=n)}return[p,f,_]}function mt(e,t,n,r,o,l){const{messageCompiler:s,warnHtmlMessage:c}=e;if(ut(r)){const e=r;return e.locale=e.locale||n,e.key=e.key||t,e}if(null==s){const e=()=>r;return e.locale=n,e.key=t,e}const u=s(r,function(e,t,n,r,o,l){return{locale:t,key:n,warnHtmlMessage:o,onError:e=>{throw l&&l(e),e},onCacheKey:e=>a(t,n,e)}}(0,n,o,0,c,l));return u.locale=n,u.key=t,u.source=r,u}function _t(...e){const[t,n,r]=e,a=m();if(!(b(t)||l(t)||ut(t)||ne(t)))throw Error(Re.INVALID_ARGUMENT);const o=l(t)?String(t):(ut(t),t);return l(n)?a.plural=n:b(n)?a.default=n:T(n)&&!u(n)?a.named=n:g(n)&&(a.list=n),l(r)?a.plural=r:b(r)?a.default=r:T(r)&&i(a,r),[o,a]}function pt(e,...t){const{datetimeFormats:n,unresolving:r,fallbackLocale:a,onWarn:o,localeFallbacker:l}=e,{__datetimeFormatters:s}=e,[c,f,m,_]=gt(...t);v(m.missingWarn)?m.missingWarn:e.missingWarn;v(m.fallbackWarn)?m.fallbackWarn:e.fallbackWarn;const p=!!m.part,d=Fe(e,m),g=l(e,a,d);if(!b(c)||""===c)return new Intl.DateTimeFormat(d,_).format(f);let E,k={},h=null;for(let u=0;u<g.length&&(E=g[u],k=n[E]||{},h=k[c],!T(h));u++)et(e,c,E,0,"datetime format");if(!T(h)||!b(E))return r?He:c;let L=`${E}__${c}`;u(_)||(L=`${L}__${JSON.stringify(_)}`);let N=s.get(L);return N||(N=new Intl.DateTimeFormat(E,i({},h,_)),s.set(L,N)),p?N.formatToParts(f):N.format(f)}const dt=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function gt(...e){const[t,n,r,a]=e,o=m();let c,u=m();if(b(t)){const e=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!e)throw Error(Re.INVALID_ISO_DATE_ARGUMENT);const n=e[3]?e[3].trim().startsWith("T")?`${e[1].trim()}${e[3].trim()}`:`${e[1].trim()}T${e[3].trim()}`:e[1].trim();c=new Date(n);try{c.toISOString()}catch(i){throw Error(Re.INVALID_ISO_DATE_ARGUMENT)}}else if(s(t)){if(isNaN(t.getTime()))throw Error(Re.INVALID_DATE_ARGUMENT);c=t}else{if(!l(t))throw Error(Re.INVALID_ARGUMENT);c=t}return b(n)?o.key=n:T(n)&&Object.keys(n).forEach((e=>{dt.includes(e)?u[e]=n[e]:o[e]=n[e]})),b(r)?o.locale=r:T(r)&&(u=r),T(a)&&(u=a),[o.key||"",c,o,u]}function Et(e,t,n){const r=e;for(const a in n){const e=`${t}__${a}`;r.__datetimeFormatters.has(e)&&r.__datetimeFormatters.delete(e)}}function bt(e,...t){const{numberFormats:n,unresolving:r,fallbackLocale:a,onWarn:o,localeFallbacker:l}=e,{__numberFormatters:s}=e,[c,f,m,_]=kt(...t);v(m.missingWarn)?m.missingWarn:e.missingWarn;v(m.fallbackWarn)?m.fallbackWarn:e.fallbackWarn;const p=!!m.part,d=Fe(e,m),g=l(e,a,d);if(!b(c)||""===c)return new Intl.NumberFormat(d,_).format(f);let E,k={},h=null;for(let u=0;u<g.length&&(E=g[u],k=n[E]||{},h=k[c],!T(h));u++)et(e,c,E,0,"number format");if(!T(h)||!b(E))return r?He:c;let L=`${E}__${c}`;u(_)||(L=`${L}__${JSON.stringify(_)}`);let N=s.get(L);return N||(N=new Intl.NumberFormat(E,i({},h,_)),s.set(L,N)),p?N.formatToParts(f):N.format(f)}const vt=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function kt(...e){const[t,n,r,a]=e,o=m();let s=m();if(!l(t))throw Error(Re.INVALID_ARGUMENT);const c=t;return b(n)?o.key=n:T(n)&&Object.keys(n).forEach((e=>{vt.includes(e)?s[e]=n[e]:o[e]=n[e]})),b(r)?o.locale=r:T(r)&&(s=r),T(a)&&(s=a),[o.key||"",c,o,s]}function ht(e,t,n){const r=e;for(const a in n){const e=`${t}__${a}`;r.__numberFormatters.has(e)&&r.__numberFormatters.delete(e)}}const Lt="9.14.4",Nt=Re.__EXTEND_POINT__,Tt=I(Nt),yt={UNEXPECTED_RETURN_TYPE:Nt,INVALID_ARGUMENT:Tt(),MUST_BE_CALL_SETUP_TOP:Tt(),NOT_INSTALLED:Tt(),NOT_AVAILABLE_IN_LEGACY_MODE:Tt(),REQUIRED_VALUE:Tt(),INVALID_VALUE:Tt(),CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:Tt(),NOT_INSTALLED_WITH_PROVIDE:Tt(),UNEXPECTED_ERROR:Tt(),NOT_COMPATIBLE_LEGACY_VUE_I18N:Tt(),BRIDGE_SUPPORT_VUE_2_ONLY:Tt(),MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION:Tt(),NOT_AVAILABLE_COMPOSITION_IN_LEGACY:Tt(),__EXTEND_POINT__:Tt()};const It=r("__translateVNode"),Ot=r("__datetimeParts"),Ct=r("__numberParts"),Pt=r("__setPluralRules"),At=r("__injectWithOption"),Rt=r("__dispose");function Ft(e){if(!k(e))return e;if(ne(e))return e;for(const t in e)if(d(e,t))if(t.includes(".")){const n=t.split("."),r=n.length-1;let a=e,o=!1;for(let e=0;e<r;e++){if("__proto__"===n[e])throw new Error(`unsafe key: ${n[e]}`);if(n[e]in a||(a[n[e]]=m()),!k(a[n[e]])){o=!0;break}a=a[n[e]]}if(o||(ne(a)?pe.includes(n[r])||delete e[t]:(a[n[r]]=e[t],delete e[t])),!ne(a)){const e=a[n[r]];k(e)&&Ft(e)}}else k(e[t])&&Ft(e[t]);return e}function St(e,t){const{messages:n,__i18n:r,messageResolver:a,flatJson:o}=t,l=T(n)?n:g(r)?m():{[e]:m()};if(g(r)&&r.forEach((e=>{if("locale"in e&&"resource"in e){const{locale:t,resource:n}=e;t?(l[t]=l[t]||m(),P(n,l[t])):P(n,l)}else b(e)&&P(JSON.parse(e),l)})),null==a&&o)for(const s in l)d(l,s)&&Ft(l[s]);return l}function Dt(e){return e.type}function Mt(e,t,n){let r=k(t.messages)?t.messages:m();"__i18nGlobal"in n&&(r=St(e.locale.value,{messages:r,__i18n:n.__i18nGlobal}));const a=Object.keys(r);if(a.length&&a.forEach((t=>{e.mergeLocaleMessage(t,r[t])})),k(t.datetimeFormats)){const n=Object.keys(t.datetimeFormats);n.length&&n.forEach((n=>{e.mergeDateTimeFormat(n,t.datetimeFormats[n])}))}if(k(t.numberFormats)){const n=Object.keys(t.numberFormats);n.length&&n.forEach((n=>{e.mergeNumberFormat(n,t.numberFormats[n])}))}}function wt(e){return t.createVNode(t.Text,null,e,0)}const xt=()=>[],Wt=()=>!1;let Ut=0;function $t(e){return(n,r,a,o)=>e(r,a,t.getCurrentInstance()||void 0,o)}function Ht(e={},r){const{__root:a,__injectWithOption:o}=e,s=void 0===a,u=e.flatJson,f=n?t.ref:t.shallowRef,m=!!e.translateExistCompatible;let _=!v(e.inheritLocale)||e.inheritLocale;const p=f(a&&_?a.locale.value:b(e.locale)?e.locale:Ve),h=f(a&&_?a.fallbackLocale.value:b(e.fallbackLocale)||g(e.fallbackLocale)||T(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:p.value),L=f(St(p.value,e)),N=f(T(e.datetimeFormats)?e.datetimeFormats:{[p.value]:{}}),y=f(T(e.numberFormats)?e.numberFormats:{[p.value]:{}});let I=a?a.missingWarn:!v(e.missingWarn)&&!c(e.missingWarn)||e.missingWarn,O=a?a.fallbackWarn:!v(e.fallbackWarn)&&!c(e.fallbackWarn)||e.fallbackWarn,C=a?a.fallbackRoot:!v(e.fallbackRoot)||e.fallbackRoot,A=!!e.fallbackFormat,R=E(e.missing)?e.missing:null,F=E(e.missing)?$t(e.missing):null,S=E(e.postTranslation)?e.postTranslation:null,D=a?a.warnHtmlMessage:!v(e.warnHtmlMessage)||e.warnHtmlMessage,M=!!e.escapeParameter;const w=a?a.modifiers:T(e.modifiers)?e.modifiers:{};let x,W=e.pluralRules||a&&a.pluralRules;x=(()=>{s&&ze(null);const t={version:Lt,locale:p.value,fallbackLocale:h.value,messages:L.value,modifiers:w,pluralRules:W,missing:null===F?void 0:F,missingWarn:I,fallbackWarn:O,fallbackFormat:A,unresolving:!0,postTranslation:null===S?void 0:S,warnHtmlMessage:D,escapeParameter:M,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};t.datetimeFormats=N.value,t.numberFormats=y.value,t.__datetimeFormatters=T(x)?x.__datetimeFormatters:void 0,t.__numberFormatters=T(x)?x.__numberFormatters:void 0;const n=qe(t);return s&&ze(n),n})(),tt(x,p.value,h.value);const U=t.computed({get:()=>p.value,set:e=>{p.value=e,x.locale=p.value}}),$=t.computed({get:()=>h.value,set:e=>{h.value=e,x.fallbackLocale=h.value,tt(x,p.value,e)}}),H=t.computed((()=>L.value)),V=t.computed((()=>N.value)),j=t.computed((()=>y.value));const X=(e,t,n,r,o,c)=>{let u;p.value,h.value,L.value,N.value,y.value;try{0,s||(x.fallbackContext=a?Je():void 0),u=e(x)}finally{s||(x.fallbackContext=void 0)}if("translate exists"!==n&&l(u)&&u===He||"translate exists"===n&&!u){const[e,n]=t();return a&&C?r(a):o(e)}if(c(u))return u;throw Error(yt.UNEXPECTED_RETURN_TYPE)};function G(...e){return X((t=>Reflect.apply(it,null,[t,...e])),(()=>_t(...e)),"translate",(t=>Reflect.apply(t.t,t,[...e])),(e=>e),(e=>b(e)))}const Y={normalize:function(e){return e.map((e=>b(e)||l(e)||v(e)?wt(String(e)):e))},interpolate:e=>e,type:"vnode"};function B(e){return L.value[e]||{}}Ut++,a&&n&&(t.watch(a.locale,(e=>{_&&(p.value=e,x.locale=e,tt(x,p.value,h.value))})),t.watch(a.fallbackLocale,(e=>{_&&(h.value=e,x.fallbackLocale=e,tt(x,p.value,h.value))})));const K={id:Ut,locale:U,fallbackLocale:$,get inheritLocale(){return _},set inheritLocale(e){_=e,e&&a&&(p.value=a.locale.value,h.value=a.fallbackLocale.value,tt(x,p.value,h.value))},get availableLocales(){return Object.keys(L.value).sort()},messages:H,get modifiers(){return w},get pluralRules(){return W||{}},get isGlobal(){return s},get missingWarn(){return I},set missingWarn(e){I=e,x.missingWarn=I},get fallbackWarn(){return O},set fallbackWarn(e){O=e,x.fallbackWarn=O},get fallbackRoot(){return C},set fallbackRoot(e){C=e},get fallbackFormat(){return A},set fallbackFormat(e){A=e,x.fallbackFormat=A},get warnHtmlMessage(){return D},set warnHtmlMessage(e){D=e,x.warnHtmlMessage=e},get escapeParameter(){return M},set escapeParameter(e){M=e,x.escapeParameter=e},t:G,getLocaleMessage:B,setLocaleMessage:function(e,t){if(u){const n={[e]:t};for(const e in n)d(n,e)&&Ft(n[e]);t=n[e]}L.value[e]=t,x.messages=L.value},mergeLocaleMessage:function(e,t){L.value[e]=L.value[e]||{};const n={[e]:t};if(u)for(const r in n)d(n,r)&&Ft(n[r]);P(t=n[e],L.value[e]),x.messages=L.value},getPostTranslationHandler:function(){return E(S)?S:null},setPostTranslationHandler:function(e){S=e,x.postTranslation=e},getMissingHandler:function(){return R},setMissingHandler:function(e){null!==e&&(F=$t(e)),R=e,x.missing=F},[Pt]:function(e){W=e,x.pluralRules=W}};return K.datetimeFormats=V,K.numberFormats=j,K.rt=function(...e){const[t,n,r]=e;if(r&&!k(r))throw Error(yt.INVALID_ARGUMENT);return G(t,n,i({resolvedMessage:!0},r||{}))},K.te=function(e,t){return X((()=>{if(!e)return!1;const n=B(b(t)?t:p.value),r=x.messageResolver(n,e);return m?null!=r:ne(r)||ut(r)||b(r)}),(()=>[e]),"translate exists",(n=>Reflect.apply(n.te,n,[e,t])),Wt,(e=>v(e)))},K.tm=function(e){const t=function(e){let t=null;const n=we(x,h.value,p.value);for(let r=0;r<n.length;r++){const a=L.value[n[r]]||{},o=x.messageResolver(a,e);if(null!=o){t=o;break}}return t}(e);return null!=t?t:a&&a.tm(e)||{}},K.d=function(...e){return X((t=>Reflect.apply(pt,null,[t,...e])),(()=>gt(...e)),"datetime format",(t=>Reflect.apply(t.d,t,[...e])),(()=>je),(e=>b(e)))},K.n=function(...e){return X((t=>Reflect.apply(bt,null,[t,...e])),(()=>kt(...e)),"number format",(t=>Reflect.apply(t.n,t,[...e])),(()=>je),(e=>b(e)))},K.getDateTimeFormat=function(e){return N.value[e]||{}},K.setDateTimeFormat=function(e,t){N.value[e]=t,x.datetimeFormats=N.value,Et(x,e,t)},K.mergeDateTimeFormat=function(e,t){N.value[e]=i(N.value[e]||{},t),x.datetimeFormats=N.value,Et(x,e,t)},K.getNumberFormat=function(e){return y.value[e]||{}},K.setNumberFormat=function(e,t){y.value[e]=t,x.numberFormats=y.value,ht(x,e,t)},K.mergeNumberFormat=function(e,t){y.value[e]=i(y.value[e]||{},t),x.numberFormats=y.value,ht(x,e,t)},K[At]=o,K[It]=function(...e){return X((t=>{let n;const r=t;try{r.processor=Y,n=Reflect.apply(it,null,[r,...e])}finally{r.processor=null}return n}),(()=>_t(...e)),"translate",(t=>t[It](...e)),(e=>[wt(e)]),(e=>g(e)))},K[Ot]=function(...e){return X((t=>Reflect.apply(pt,null,[t,...e])),(()=>gt(...e)),"datetime format",(t=>t[Ot](...e)),xt,(e=>b(e)||g(e)))},K[Ct]=function(...e){return X((t=>Reflect.apply(bt,null,[t,...e])),(()=>kt(...e)),"number format",(t=>t[Ct](...e)),xt,(e=>b(e)||g(e)))},K}function Vt(e={},t){{const t=Ht(function(e){const t=b(e.locale)?e.locale:Ve,n=b(e.fallbackLocale)||g(e.fallbackLocale)||T(e.fallbackLocale)||!1===e.fallbackLocale?e.fallbackLocale:t,r=E(e.missing)?e.missing:void 0,a=!v(e.silentTranslationWarn)&&!c(e.silentTranslationWarn)||!e.silentTranslationWarn,o=!v(e.silentFallbackWarn)&&!c(e.silentFallbackWarn)||!e.silentFallbackWarn,l=!v(e.fallbackRoot)||e.fallbackRoot,s=!!e.formatFallbackMessages,u=T(e.modifiers)?e.modifiers:{},f=e.pluralizationRules,m=E(e.postTranslation)?e.postTranslation:void 0,_=!b(e.warnHtmlInMessage)||"off"!==e.warnHtmlInMessage,p=!!e.escapeParameterHtml,d=!v(e.sync)||e.sync;let k=e.messages;if(T(e.sharedMessages)){const t=e.sharedMessages;k=Object.keys(t).reduce(((e,n)=>{const r=e[n]||(e[n]={});return i(r,t[n]),e}),k||{})}const{__i18n:h,__root:L,__injectWithOption:N}=e,y=e.datetimeFormats,I=e.numberFormats,O=e.flatJson,C=e.translateExistCompatible;return{locale:t,fallbackLocale:n,messages:k,flatJson:O,datetimeFormats:y,numberFormats:I,missing:r,missingWarn:a,fallbackWarn:o,fallbackRoot:l,fallbackFormat:s,modifiers:u,pluralRules:f,postTranslation:m,warnHtmlMessage:_,escapeParameter:p,messageResolver:e.messageResolver,inheritLocale:d,translateExistCompatible:C,__i18n:h,__root:L,__injectWithOption:N}}(e)),{__extender:n}=e,r={id:t.id,get locale(){return t.locale.value},set locale(e){t.locale.value=e},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(e){t.fallbackLocale.value=e},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get formatter(){return{interpolate:()=>[]}},set formatter(e){},get missing(){return t.getMissingHandler()},set missing(e){t.setMissingHandler(e)},get silentTranslationWarn(){return v(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(e){t.missingWarn=v(e)?!e:e},get silentFallbackWarn(){return v(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(e){t.fallbackWarn=v(e)?!e:e},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(e){t.fallbackFormat=e},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(e){t.setPostTranslationHandler(e)},get sync(){return t.inheritLocale},set sync(e){t.inheritLocale=e},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(e){t.warnHtmlMessage="off"!==e},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(e){t.escapeParameter=e},get preserveDirectiveContent(){return!0},set preserveDirectiveContent(e){},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...e){const[n,r,a]=e,o={};let l=null,s=null;if(!b(n))throw Error(yt.INVALID_ARGUMENT);const c=n;return b(r)?o.locale=r:g(r)?l=r:T(r)&&(s=r),g(a)?l=a:T(a)&&(s=a),Reflect.apply(t.t,t,[c,l||s||{},o])},rt:(...e)=>Reflect.apply(t.rt,t,[...e]),tc(...e){const[n,r,a]=e,o={plural:1};let s=null,c=null;if(!b(n))throw Error(yt.INVALID_ARGUMENT);const u=n;return b(r)?o.locale=r:l(r)?o.plural=r:g(r)?s=r:T(r)&&(c=r),b(a)?o.locale=a:g(a)?s=a:T(a)&&(c=a),Reflect.apply(t.t,t,[u,s||c||{},o])},te:(e,n)=>t.te(e,n),tm:e=>t.tm(e),getLocaleMessage:e=>t.getLocaleMessage(e),setLocaleMessage(e,n){t.setLocaleMessage(e,n)},mergeLocaleMessage(e,n){t.mergeLocaleMessage(e,n)},d:(...e)=>Reflect.apply(t.d,t,[...e]),getDateTimeFormat:e=>t.getDateTimeFormat(e),setDateTimeFormat(e,n){t.setDateTimeFormat(e,n)},mergeDateTimeFormat(e,n){t.mergeDateTimeFormat(e,n)},n:(...e)=>Reflect.apply(t.n,t,[...e]),getNumberFormat:e=>t.getNumberFormat(e),setNumberFormat(e,n){t.setNumberFormat(e,n)},mergeNumberFormat(e,n){t.mergeNumberFormat(e,n)},getChoiceIndex:(e,t)=>-1};return r.__extender=n,r}}const jt={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>"parent"===e||"global"===e,default:"parent"},i18n:{type:Object}};function Xt(e){return t.Fragment}const Gt=t.defineComponent({name:"i18n-t",props:i({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>l(e)||!isNaN(e)}},jt),setup(e,n){const{slots:r,attrs:a}=n,o=e.i18n||rn({useScope:e.scope,__useComponent:!0});return()=>{const l=Object.keys(r).filter((e=>"_"!==e)),s=m();e.locale&&(s.locale=e.locale),void 0!==e.plural&&(s.plural=b(e.plural)?+e.plural:e.plural);const c=function({slots:e},n){if(1===n.length&&"default"===n[0])return(e.default?e.default():[]).reduce(((e,n)=>[...e,...n.type===t.Fragment?n.children:[n]]),[]);return n.reduce(((t,n)=>{const r=e[n];return r&&(t[n]=r()),t}),m())}(n,l),u=o[It](e.keypath,c,s),f=i(m(),a),_=b(e.tag)||k(e.tag)?e.tag:Xt();return t.h(_,f,u)}}}),Yt=Gt;function Bt(e,n,r,a){const{slots:o,attrs:l}=n;return()=>{const n={part:!0};let s=m();e.locale&&(n.locale=e.locale),b(e.format)?n.key=e.format:k(e.format)&&(b(e.format.key)&&(n.key=e.format.key),s=Object.keys(e.format).reduce(((t,n)=>r.includes(n)?i(m(),t,{[n]:e.format[n]}):t),m()));const c=a(e.value,n,s);let u=[n.key];g(c)?u=c.map(((e,t)=>{const n=o[e.type],r=n?n({[e.type]:e.value,index:t,parts:c}):[e.value];var a;return g(a=r)&&!b(a[0])&&(r[0].key=`${e.type}-${t}`),r})):b(c)&&(u=[c]);const f=i(m(),l),_=b(e.tag)||k(e.tag)?e.tag:Xt();return t.h(_,f,u)}}const Kt=t.defineComponent({name:"i18n-n",props:i({value:{type:Number,required:!0},format:{type:[String,Object]}},jt),setup(e,t){const n=e.i18n||rn({useScope:e.scope,__useComponent:!0});return Bt(e,t,vt,((...e)=>n[Ct](...e)))}}),zt=Kt,Jt=t.defineComponent({name:"i18n-d",props:i({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},jt),setup(e,t){const n=e.i18n||rn({useScope:e.scope,__useComponent:!0});return Bt(e,t,dt,((...e)=>n[Ot](...e)))}}),Qt=Jt;function qt(e){const r=t=>{const{instance:n,modifiers:r,value:a}=t;if(!n||!n.$)throw Error(yt.UNEXPECTED_ERROR);const o=function(e,t){const n=e;if("composition"===e.mode)return n.__getInstance(t)||e.global;{const r=n.__getInstance(t);return null!=r?r.__composer:e.global.__composer}}(e,n.$),l=Zt(a);return[Reflect.apply(o.t,o,[...en(l)]),o]};return{created:(a,o)=>{const[l,s]=r(o);n&&e.global===s&&(a.__i18nWatcher=t.watch(s.locale,(()=>{o.instance&&o.instance.$forceUpdate()}))),a.__composer=s,a.textContent=l},unmounted:e=>{n&&e.__i18nWatcher&&(e.__i18nWatcher(),e.__i18nWatcher=void 0,delete e.__i18nWatcher),e.__composer&&(e.__composer=void 0,delete e.__composer)},beforeUpdate:(e,{value:t})=>{if(e.__composer){const n=e.__composer,r=Zt(t);e.textContent=Reflect.apply(n.t,n,[...en(r)])}},getSSRProps:e=>{const[t]=r(e);return{textContent:t}}}}function Zt(e){if(b(e))return{path:e};if(T(e)){if(!("path"in e))throw Error(yt.REQUIRED_VALUE,"path");return e}throw Error(yt.INVALID_VALUE)}function en(e){const{path:t,locale:n,args:r,choice:a,plural:o}=e,s={},c=r||{};return b(n)&&(s.locale=n),l(a)&&(s.plural=a),l(o)&&(s.plural=o),[t,c,s]}function tn(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[Pt](t.pluralizationRules||e.pluralizationRules);const n=St(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(n).forEach((t=>e.mergeLocaleMessage(t,n[t]))),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach((n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n]))),t.numberFormats&&Object.keys(t.numberFormats).forEach((n=>e.mergeNumberFormat(n,t.numberFormats[n]))),e}const nn=r("global-vue-i18n");function rn(e={}){const n=t.getCurrentInstance();if(null==n)throw Error(yt.MUST_BE_CALL_SETUP_TOP);if(!n.isCE&&null!=n.appContext.app&&!n.appContext.app.__VUE_I18N_SYMBOL__)throw Error(yt.NOT_INSTALLED);const r=function(e){{const n=t.inject(e.isCE?nn:e.appContext.app.__VUE_I18N_SYMBOL__);if(!n)throw function(e,...t){return S(e,null,void 0)}(e.isCE?yt.NOT_INSTALLED_WITH_PROVIDE:yt.UNEXPECTED_ERROR);return n}}(n),a=function(e){return"composition"===e.mode?e.global:e.global.__composer}(r),o=Dt(n),l=function(e,t){return u(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}(e,o);if("legacy"===r.mode&&!e.__useComponent){if(!r.allowComposition)throw Error(yt.NOT_AVAILABLE_IN_LEGACY_MODE);return function(e,n,r,a={}){const o="local"===n,l=t.shallowRef(null);if(o&&e.proxy&&!e.proxy.$options.i18n&&!e.proxy.$options.__i18n)throw Error(yt.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);const s=v(a.inheritLocale)?a.inheritLocale:!b(a.locale),u=t.ref(!o||s?r.locale.value:b(a.locale)?a.locale:Ve),i=t.ref(!o||s?r.fallbackLocale.value:b(a.fallbackLocale)||g(a.fallbackLocale)||T(a.fallbackLocale)||!1===a.fallbackLocale?a.fallbackLocale:u.value),f=t.ref(St(u.value,a)),m=t.ref(T(a.datetimeFormats)?a.datetimeFormats:{[u.value]:{}}),_=t.ref(T(a.numberFormats)?a.numberFormats:{[u.value]:{}}),p=o?r.missingWarn:!v(a.missingWarn)&&!c(a.missingWarn)||a.missingWarn,d=o?r.fallbackWarn:!v(a.fallbackWarn)&&!c(a.fallbackWarn)||a.fallbackWarn,k=o?r.fallbackRoot:!v(a.fallbackRoot)||a.fallbackRoot,h=!!a.fallbackFormat,L=E(a.missing)?a.missing:null,N=E(a.postTranslation)?a.postTranslation:null,y=o?r.warnHtmlMessage:!v(a.warnHtmlMessage)||a.warnHtmlMessage,I=!!a.escapeParameter,O=o?r.modifiers:T(a.modifiers)?a.modifiers:{},C=a.pluralRules||o&&r.pluralRules;function P(){return[u.value,i.value,f.value,m.value,_.value]}const A=t.computed({get:()=>l.value?l.value.locale.value:u.value,set:e=>{l.value&&(l.value.locale.value=e),u.value=e}}),R=t.computed({get:()=>l.value?l.value.fallbackLocale.value:i.value,set:e=>{l.value&&(l.value.fallbackLocale.value=e),i.value=e}}),F=t.computed((()=>l.value?l.value.messages.value:f.value)),S=t.computed((()=>m.value)),D=t.computed((()=>_.value));function M(){return l.value?l.value.getPostTranslationHandler():N}function w(e){l.value&&l.value.setPostTranslationHandler(e)}function x(){return l.value?l.value.getMissingHandler():L}function W(e){l.value&&l.value.setMissingHandler(e)}function U(e){return P(),e()}function $(...e){return l.value?U((()=>Reflect.apply(l.value.t,null,[...e]))):U((()=>""))}function H(...e){return l.value?Reflect.apply(l.value.rt,null,[...e]):""}function V(...e){return l.value?U((()=>Reflect.apply(l.value.d,null,[...e]))):U((()=>""))}function j(...e){return l.value?U((()=>Reflect.apply(l.value.n,null,[...e]))):U((()=>""))}function X(e){return l.value?l.value.tm(e):{}}function G(e,t){return!!l.value&&l.value.te(e,t)}function Y(e){return l.value?l.value.getLocaleMessage(e):{}}function B(e,t){l.value&&(l.value.setLocaleMessage(e,t),f.value[e]=t)}function K(e,t){l.value&&l.value.mergeLocaleMessage(e,t)}function z(e){return l.value?l.value.getDateTimeFormat(e):{}}function J(e,t){l.value&&(l.value.setDateTimeFormat(e,t),m.value[e]=t)}function Q(e,t){l.value&&l.value.mergeDateTimeFormat(e,t)}function q(e){return l.value?l.value.getNumberFormat(e):{}}function Z(e,t){l.value&&(l.value.setNumberFormat(e,t),_.value[e]=t)}function ee(e,t){l.value&&l.value.mergeNumberFormat(e,t)}const te={get id(){return l.value?l.value.id:-1},locale:A,fallbackLocale:R,messages:F,datetimeFormats:S,numberFormats:D,get inheritLocale(){return l.value?l.value.inheritLocale:s},set inheritLocale(e){l.value&&(l.value.inheritLocale=e)},get availableLocales(){return l.value?l.value.availableLocales:Object.keys(f.value)},get modifiers(){return l.value?l.value.modifiers:O},get pluralRules(){return l.value?l.value.pluralRules:C},get isGlobal(){return!!l.value&&l.value.isGlobal},get missingWarn(){return l.value?l.value.missingWarn:p},set missingWarn(e){l.value&&(l.value.missingWarn=e)},get fallbackWarn(){return l.value?l.value.fallbackWarn:d},set fallbackWarn(e){l.value&&(l.value.missingWarn=e)},get fallbackRoot(){return l.value?l.value.fallbackRoot:k},set fallbackRoot(e){l.value&&(l.value.fallbackRoot=e)},get fallbackFormat(){return l.value?l.value.fallbackFormat:h},set fallbackFormat(e){l.value&&(l.value.fallbackFormat=e)},get warnHtmlMessage(){return l.value?l.value.warnHtmlMessage:y},set warnHtmlMessage(e){l.value&&(l.value.warnHtmlMessage=e)},get escapeParameter(){return l.value?l.value.escapeParameter:I},set escapeParameter(e){l.value&&(l.value.escapeParameter=e)},t:$,getPostTranslationHandler:M,setPostTranslationHandler:w,getMissingHandler:x,setMissingHandler:W,rt:H,d:V,n:j,tm:X,te:G,getLocaleMessage:Y,setLocaleMessage:B,mergeLocaleMessage:K,getDateTimeFormat:z,setDateTimeFormat:J,mergeDateTimeFormat:Q,getNumberFormat:q,setNumberFormat:Z,mergeNumberFormat:ee};function ne(e){e.locale.value=u.value,e.fallbackLocale.value=i.value,Object.keys(f.value).forEach((t=>{e.mergeLocaleMessage(t,f.value[t])})),Object.keys(m.value).forEach((t=>{e.mergeDateTimeFormat(t,m.value[t])})),Object.keys(_.value).forEach((t=>{e.mergeNumberFormat(t,_.value[t])})),e.escapeParameter=I,e.fallbackFormat=h,e.fallbackRoot=k,e.fallbackWarn=d,e.missingWarn=p,e.warnHtmlMessage=y}return t.onBeforeMount((()=>{if(null==e.proxy||null==e.proxy.$i18n)throw Error(yt.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);const t=l.value=e.proxy.$i18n.__composer;"global"===n?(u.value=t.locale.value,i.value=t.fallbackLocale.value,f.value=t.messages.value,m.value=t.datetimeFormats.value,_.value=t.numberFormats.value):o&&ne(t)})),te}(n,l,a,e)}if("global"===l)return Mt(a,e,o),a;if("parent"===l){let t=function(e,t,n=!1){let r=null;const a=t.root;let o=function(e,t=!1){if(null==e)return null;return t&&e.vnode.ctx||e.parent}(t,n);for(;null!=o;){const t=e;if("composition"===e.mode)r=t.__getInstance(o);else{const e=t.__getInstance(o);null!=e&&(r=e.__composer,n&&r&&!r[At]&&(r=null))}if(null!=r)break;if(a===o)break;o=o.parent}return r}(r,n,e.__useComponent);return null==t&&(t=a),t}const s=r;let f=s.__getInstance(n);if(null==f){const r=i({},e);"__i18n"in o&&(r.__i18n=o.__i18n),a&&(r.__root=a),f=Ht(r),s.__composerExtend&&(f[Rt]=s.__composerExtend(f)),function(e,n,r){t.onMounted((()=>{}),n),t.onUnmounted((()=>{const t=r;e.__deleteInstance(n);const a=t[Rt];a&&(a(),delete t[Rt])}),n)}(s,n,f),s.__setInstance(n,f)}return f}const an=["locale","fallbackLocale","availableLocales"],on=["t","rt","d","n","tm","te"];return Ge=function(e,t){if(b(e)){!v(t.warnHtmlMessage)||t.warnHtmlMessage;const n=(t.onCacheKey||lt)(e),r=st[n];if(r)return r;const{ast:a,detectError:o}=function(e,t={}){let n=!1;const r=t.onError||D;return t.onError=e=>{n=!0,r(e)},{...te(e,t),detectError:n}}(e,{...t,location:!1,jit:!0}),l=rt(a);return o?l:st[n]=l}{const t=e.cacheKey;return t?st[t]||(st[t]=rt(e)):rt(e)}},Ye=function(e,t){if(!k(e))return null;let n=ke.get(t);if(n||(n=function(e){const t=[];let n,r,a,o,l,s,c,u=-1,i=0,f=0;const m=[];function _(){const t=e[u+1];if(5===i&&"'"===t||6===i&&'"'===t)return u++,a="\\"+t,m[0](),!0}for(m[0]=()=>{void 0===r?r=a:r+=a},m[1]=()=>{void 0!==r&&(t.push(r),r=void 0)},m[2]=()=>{m[0](),f++},m[3]=()=>{if(f>0)f--,i=4,m[0]();else{if(f=0,void 0===r)return!1;if(r=ve(r),!1===r)return!1;m[1]()}};null!==i;)if(u++,n=e[u],"\\"!==n||!_()){if(o=be(n),c=ge[i],l=c[o]||c.l||8,8===l)return;if(i=l[0],void 0!==l[1]&&(s=m[l[1]],s&&(a=n,!1===s())))return;if(7===i)return t}}(t),n&&ke.set(t,n)),!n)return null;const r=n.length;let a=e,o=0;for(;o<r;){const e=n[o];if(pe.includes(e)&&ne(a))return null;const t=a[e];if(void 0===t)return null;if(E(a))return null;a=t,o++}return a},Be=we,e.DatetimeFormat=Jt,e.I18nD=Qt,e.I18nInjectionKey=nn,e.I18nN=zt,e.I18nT=Yt,e.NumberFormat=Kt,e.Translation=Gt,e.VERSION=Lt,e.castToVueI18n=e=>{if(!("__VUE_I18N_BRIDGE__"in e))throw Error(yt.NOT_COMPATIBLE_LEGACY_VUE_I18N);return e},e.createI18n=function(e={},n){const a=!v(e.legacy)||e.legacy,o=!v(e.globalInjection)||e.globalInjection,l=!a||!!e.allowComposition,s=new Map,[c,u]=function(e,n,r){const a=t.effectScope();{const t=n?a.run((()=>Vt(e))):a.run((()=>Ht(e)));if(null==t)throw Error(yt.UNEXPECTED_ERROR);return[a,t]}}(e,a),i=r("");{const e={get mode(){return a?"legacy":"composition"},get allowComposition(){return l},async install(n,...r){if(n.__VUE_I18N_SYMBOL__=i,n.provide(n.__VUE_I18N_SYMBOL__,e),T(r[0])){const t=r[0];e.__composerExtend=t.__composerExtend,e.__vueI18nExtend=t.__vueI18nExtend}let l=null;!a&&o&&(l=function(e,n){const r=Object.create(null);an.forEach((e=>{const a=Object.getOwnPropertyDescriptor(n,e);if(!a)throw Error(yt.UNEXPECTED_ERROR);const o=t.isRef(a.value)?{get:()=>a.value.value,set(e){a.value.value=e}}:{get:()=>a.get&&a.get()};Object.defineProperty(r,e,o)})),e.config.globalProperties.$i18n=r,on.forEach((t=>{const r=Object.getOwnPropertyDescriptor(n,t);if(!r||!r.value)throw Error(yt.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${t}`,r)}));const a=()=>{delete e.config.globalProperties.$i18n,on.forEach((t=>{delete e.config.globalProperties[`$${t}`]}))};return a}(n,e.global)),function(e,t,...n){const r=T(n[0])?n[0]:{},a=!!r.useI18nComponentName;(!v(r.globalInstall)||r.globalInstall)&&([a?"i18n":Gt.name,"I18nT"].forEach((t=>e.component(t,Gt))),[Kt.name,"I18nN"].forEach((t=>e.component(t,Kt))),[Jt.name,"I18nD"].forEach((t=>e.component(t,Jt)))),e.directive("t",qt(t))}(n,e,...r),a&&n.mixin(function(e,n,r){return{beforeCreate(){const a=t.getCurrentInstance();if(!a)throw Error(yt.UNEXPECTED_ERROR);const o=this.$options;if(o.i18n){const t=o.i18n;if(o.__i18n&&(t.__i18n=o.__i18n),t.__root=n,this===this.$root)this.$i18n=tn(e,t);else{t.__injectWithOption=!0,t.__extender=r.__vueI18nExtend,this.$i18n=Vt(t);const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}}else if(o.__i18n)if(this===this.$root)this.$i18n=tn(e,o);else{this.$i18n=Vt({__i18n:o.__i18n,__injectWithOption:!0,__extender:r.__vueI18nExtend,__root:n});const e=this.$i18n;e.__extender&&(e.__disposer=e.__extender(this.$i18n))}else this.$i18n=e;o.__i18nGlobal&&Mt(n,o,o),this.$t=(...e)=>this.$i18n.t(...e),this.$rt=(...e)=>this.$i18n.rt(...e),this.$tc=(...e)=>this.$i18n.tc(...e),this.$te=(e,t)=>this.$i18n.te(e,t),this.$d=(...e)=>this.$i18n.d(...e),this.$n=(...e)=>this.$i18n.n(...e),this.$tm=e=>this.$i18n.tm(e),r.__setInstance(a,this.$i18n)},mounted(){},unmounted(){const e=t.getCurrentInstance();if(!e)throw Error(yt.UNEXPECTED_ERROR);const n=this.$i18n;delete this.$t,delete this.$rt,delete this.$tc,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,n.__disposer&&(n.__disposer(),delete n.__disposer,delete n.__extender),r.__deleteInstance(e),delete this.$i18n}}}(u,u.__composer,e));const s=n.unmount;n.unmount=()=>{l&&l(),e.dispose(),s()}},get global(){return u},dispose(){c.stop()},__instances:s,__getInstance:function(e){return s.get(e)||null},__setInstance:function(e,t){s.set(e,t)},__deleteInstance:function(e){s.delete(e)}};return e}},e.useI18n=rn,e.vTDirective=qt,e}({},Vue);
