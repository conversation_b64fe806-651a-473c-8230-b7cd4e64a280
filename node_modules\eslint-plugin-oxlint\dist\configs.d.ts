import { Lin<PERSON> } from 'eslint';
declare const _default: {
    "flat/pedantic": Linter.Config<Linter.RulesRecord>[];
    "flat/suspicious": Linter.Config<Linter.RulesRecord>[];
    "flat/style": Linter.Config<Linter.RulesRecord>[];
    "flat/restriction": Linter.Config<Linter.RulesRecord>[];
    "flat/correctness": Linter.Config<Linter.RulesRecord>[];
    "flat/perf": Linter.Config<Linter.RulesRecord>[];
    "flat/eslint": Linter.Config<Linter.RulesRecord>[];
    "flat/import": Linter.Config<Linter.RulesRecord>[];
    "flat/jest": Linter.Config<Linter.RulesRecord>[];
    "flat/jsdoc": Linter.Config<Linter.RulesRecord>[];
    "flat/jsx-a11y": Linter.Config<Linter.RulesRecord>[];
    "flat/nextjs": Linter.Config<Linter.RulesRecord>[];
    "flat/node": Linter.Config<Linter.RulesRecord>[];
    "flat/promise": Linter.Config<Linter.RulesRecord>[];
    "flat/react": Linter.Config<Linter.RulesRecord>[];
    "flat/react-hooks": Linter.Config<Linter.RulesRecord>[];
    "flat/react-perf": Linter.Config<Linter.RulesRecord>[];
    "flat/typescript": Linter.Config<Linter.RulesRecord>[];
    "flat/unicorn": Linter.Config<Linter.RulesRecord>[];
    "flat/vitest": Linter.Config<Linter.RulesRecord>[];
    recommended: Linter.LegacyConfig<Record<string, "off">, Record<string, "off">>;
    all: Linter.LegacyConfig<Record<string, "off">, Record<string, "off">>;
    'flat/all': Linter.Config<Linter.RulesRecord>[];
    'flat/recommended': Linter.Config<Linter.RulesRecord>[];
};
export default _default;
