<template>
  <div
    :class="[
      'bg-white rounded-2xl border-t-0 border-slate-300',
      paddingClass,
      shadowClass,
      customClass
    ]"
  >
    <slot />
  </div>
</template>

<script setup lang="ts">
interface Props {
  padding?: 'none' | 'sm' | 'md' | 'lg'
  shadow?: boolean
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  padding: 'md',
  shadow: true,
  customClass: ''
})

const paddingClass = computed(() => {
  const paddingMap = {
    none: '',
    sm: 'p-2',
    md: 'p-3.5',
    lg: 'p-6'
  }
  return paddingMap[props.padding]
})

const shadowClass = computed(() => {
  return props.shadow ? 'shadow-[0px_2px_12px_rgba(0,0,0,0.05)]' : ''
})
</script>
