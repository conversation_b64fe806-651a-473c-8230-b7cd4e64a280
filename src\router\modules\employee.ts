import type { RouteRecordRaw } from "vue-router";

const employeeRoutes: RouteRecordRaw[] = [
  {
    path: "/employee",
    name: "EmployeeDashboard",
    component: () => import("@/layouts/employee/EmployeeLayout.vue"),
    meta: {
      title: "Employee Dashboard",
      requiresAuth: true,
      role: "employee",
    },
    children: [
      {
        path: "",
        name: "EmployeeDashboardHome",
        component: () => import("@/components/dashboard-lead-analysis/LeadAnalysisDashboard.vue"),
        meta: {
          title: "Employee Dashboard",
        },
      },
    ],
  },
  {
    path: "/employee-new",
    component: () => import("@/layouts/employee/EmployeeLayout.vue"),
    meta: {
      requiresAuth: true,
      role: "employee",
    },
    children: [
      // Dashboard
      {
        path: "",
        name: "EmployeeDashboardNew",
        component: () => import("@/views/employee/DashboardView.vue"),
        meta: {
          title: "Employee Dashboard New",
        },
      },
      {
        path: "lead-analysis",
        name: "EmployeeLeadAnalysis",
        component: () => import("@/views/employee/LeadAnalysisView.vue"),
        meta: {
          title: "Lead Analysis",
        },
      },

      // Applications
      {
        path: "applications",
        children: [
          {
            path: "incoming",
            name: "EmployeeIncomingApplications",
            component: () => import("@/views/employee/applications/IncomingView.vue"),
            meta: { title: "Incoming Applications" },
          },
          {
            path: "pending",
            name: "EmployeePendingApplications",
            component: () => import("@/views/employee/applications/PendingView.vue"),
            meta: { title: "Pending Applications" },
          },
          {
            path: "new",
            name: "EmployeeNewApplication",
            component: () => import("@/views/employee/applications/NewView.vue"),
            meta: { title: "New Application" },
          },
        ],
      },

      // Loans
      {
        path: "loans",
        name: "EmployeeLoans",
        component: () => import("@/views/employee/LoansView.vue"),
        meta: { title: "Loans" },
      },

      // Customers
      {
        path: "customers",
        name: "EmployeeCustomers",
        component: () => import("@/views/employee/CustomersView.vue"),
        meta: { title: "Customer Query" },
      },

      // Reports
      {
        path: "reports",
        children: [
          {
            path: "",
            name: "EmployeeReports",
            component: () => import("@/views/employee/reports/IndexView.vue"),
            meta: { title: "Reports" },
          },
          {
            path: "builder",
            name: "EmployeeReportBuilder",
            component: () => import("@/views/employee/reports/BuilderView.vue"),
            meta: { title: "Report Builder" },
          },
          {
            path: "results",
            name: "EmployeeReportResults",
            component: () => import("@/views/employee/reports/ResultsView.vue"),
            meta: { title: "Report Results" },
          },
        ],
      },

      // ACH
      {
        path: "ach",
        children: [
          {
            path: "batch-processing",
            name: "EmployeeACHBatchProcessing",
            component: () => import("@/views/employee/ach/BatchProcessingView.vue"),
            meta: { title: "ACH Batch Processing Queue" },
          },
          {
            path: "batch-renewal",
            name: "EmployeeACHBatchRenewal",
            component: () => import("@/views/employee/ach/BatchRenewalView.vue"),
            meta: { title: "ACH Batch Renewal Queue" },
          },
          {
            path: "batch-files",
            name: "EmployeeACHBatchFiles",
            component: () => import("@/views/employee/ach/BatchFilesView.vue"),
            meta: { title: "Batch Files Management" },
          },
        ],
      },

      // Information Tables
      {
        path: "info",
        children: [
          {
            path: "bad-aba",
            name: "EmployeeBadABA",
            component: () => import("@/views/employee/info/BadABAView.vue"),
            meta: { title: "Bad ABA Numbers" },
          },
          {
            path: "bad-employment",
            name: "EmployeeBadEmployment",
            component: () => import("@/views/employee/info/BadEmploymentView.vue"),
            meta: { title: "Bad Employment" },
          },
        ],
      },

      // Search
      {
        path: "search",
        name: "EmployeeSearch",
        component: () => import("@/views/employee/SearchView.vue"),
        meta: { title: "Search Results" },
      },
    ],
  },
];

export default employeeRoutes;
