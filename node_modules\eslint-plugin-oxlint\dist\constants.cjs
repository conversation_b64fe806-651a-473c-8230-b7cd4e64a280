"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const aliasPluginNames = {
  // for scripts/generate and src/build-from-oxlint-config
  eslint: "",
  typescript: "@typescript-eslint",
  nextjs: "@next/next",
  // only for src/build-from-oxlint-config
  react_perf: "react-perf",
  jsx_a11y: "jsx-a11y",
  "import-x": "import"
};
const reactHookRulesInsideReactScope = [
  "rules-of-hooks",
  "exhaustive-deps"
];
const rulesDisabledForVueAndSvelteFiles = [
  "no-unused-vars",
  "@typescript-eslint/no-unused-vars",
  "react-hooks/rules-of-hooks"
  // disabled because its react
];
exports.aliasPluginNames = aliasPluginNames;
exports.reactHookRulesInsideReactScope = reactHookRulesInsideReactScope;
exports.rulesDisabledForVueAndSvelteFiles = rulesDisabledForVueAndSvelteFiles;
