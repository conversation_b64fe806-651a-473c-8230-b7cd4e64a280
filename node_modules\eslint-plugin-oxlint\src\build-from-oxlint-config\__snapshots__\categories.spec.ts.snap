// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`handleCategoriesScope > custom plugins, custom categories > customPluginCustomCategories 1`] = `
{
  "block-scoped-var": "off",
  "import/no-absolute-path": "off",
  "import/no-duplicates": "off",
  "import/no-empty-named-blocks": "off",
  "import/no-named-as-default": "off",
  "import/no-named-as-default-member": "off",
  "import/no-self-import": "off",
  "import/no-unassigned-import": "off",
  "no-extend-native": "off",
  "no-extra-bind": "off",
  "no-new": "off",
  "no-unexpected-multiline": "off",
  "no-unneeded-ternary": "off",
  "no-useless-concat": "off",
  "no-useless-constructor": "off",
}
`;

exports[`handleCategoriesScope > custom plugins, default categories > customPluginDefaultCategories 1`] = `
{
  "for-direction": "off",
  "no-async-promise-executor": "off",
  "no-caller": "off",
  "no-class-assign": "off",
  "no-compare-neg-zero": "off",
  "no-cond-assign": "off",
  "no-const-assign": "off",
  "no-constant-binary-expression": "off",
  "no-constant-condition": "off",
  "no-control-regex": "off",
  "no-debugger": "off",
  "no-delete-var": "off",
  "no-dupe-class-members": "off",
  "no-dupe-else-if": "off",
  "no-dupe-keys": "off",
  "no-duplicate-case": "off",
  "no-empty-character-class": "off",
  "no-empty-pattern": "off",
  "no-empty-static-block": "off",
  "no-eval": "off",
  "no-ex-assign": "off",
  "no-extra-boolean-cast": "off",
  "no-func-assign": "off",
  "no-global-assign": "off",
  "no-import-assign": "off",
  "no-invalid-regexp": "off",
  "no-irregular-whitespace": "off",
  "no-loss-of-precision": "off",
  "no-new-native-nonconstructor": "off",
  "no-nonoctal-decimal-escape": "off",
  "no-obj-calls": "off",
  "no-self-assign": "off",
  "no-setter-return": "off",
  "no-shadow-restricted-names": "off",
  "no-sparse-arrays": "off",
  "no-this-before-super": "off",
  "no-unsafe-finally": "off",
  "no-unsafe-negation": "off",
  "no-unsafe-optional-chaining": "off",
  "no-unused-labels": "off",
  "no-unused-private-class-members": "off",
  "no-unused-vars": "off",
  "no-useless-backreference": "off",
  "no-useless-catch": "off",
  "no-useless-escape": "off",
  "no-useless-rename": "off",
  "no-with": "off",
  "require-yield": "off",
  "unicorn/no-await-in-promise-methods": "off",
  "unicorn/no-empty-file": "off",
  "unicorn/no-invalid-fetch-options": "off",
  "unicorn/no-invalid-remove-event-listener": "off",
  "unicorn/no-new-array": "off",
  "unicorn/no-single-promise-in-promise-methods": "off",
  "unicorn/no-thenable": "off",
  "unicorn/no-unnecessary-await": "off",
  "unicorn/no-useless-fallback-in-spread": "off",
  "unicorn/no-useless-length-check": "off",
  "unicorn/no-useless-spread": "off",
  "unicorn/prefer-set-size": "off",
  "unicorn/prefer-string-starts-ends-with": "off",
  "use-isnan": "off",
  "valid-typeof": "off",
}
`;

exports[`handleCategoriesScope > default plugins (react, unicorn, typescript), default categories > defaultPluginDefaultCategories 1`] = `
{
  "@typescript-eslint/no-dupe-class-members": "off",
  "@typescript-eslint/no-duplicate-enum-values": "off",
  "@typescript-eslint/no-extra-non-null-assertion": "off",
  "@typescript-eslint/no-loss-of-precision": "off",
  "@typescript-eslint/no-misused-new": "off",
  "@typescript-eslint/no-non-null-asserted-optional-chain": "off",
  "@typescript-eslint/no-this-alias": "off",
  "@typescript-eslint/no-unnecessary-parameter-property-assignment": "off",
  "@typescript-eslint/no-unsafe-declaration-merging": "off",
  "@typescript-eslint/no-unused-vars": "off",
  "@typescript-eslint/no-useless-empty-export": "off",
  "@typescript-eslint/no-wrapper-object-types": "off",
  "@typescript-eslint/prefer-as-const": "off",
  "@typescript-eslint/triple-slash-reference": "off",
  "for-direction": "off",
  "no-async-promise-executor": "off",
  "no-caller": "off",
  "no-class-assign": "off",
  "no-compare-neg-zero": "off",
  "no-cond-assign": "off",
  "no-const-assign": "off",
  "no-constant-binary-expression": "off",
  "no-constant-condition": "off",
  "no-control-regex": "off",
  "no-debugger": "off",
  "no-delete-var": "off",
  "no-dupe-class-members": "off",
  "no-dupe-else-if": "off",
  "no-dupe-keys": "off",
  "no-duplicate-case": "off",
  "no-empty-character-class": "off",
  "no-empty-pattern": "off",
  "no-empty-static-block": "off",
  "no-eval": "off",
  "no-ex-assign": "off",
  "no-extra-boolean-cast": "off",
  "no-func-assign": "off",
  "no-global-assign": "off",
  "no-import-assign": "off",
  "no-invalid-regexp": "off",
  "no-irregular-whitespace": "off",
  "no-loss-of-precision": "off",
  "no-new-native-nonconstructor": "off",
  "no-nonoctal-decimal-escape": "off",
  "no-obj-calls": "off",
  "no-self-assign": "off",
  "no-setter-return": "off",
  "no-shadow-restricted-names": "off",
  "no-sparse-arrays": "off",
  "no-this-before-super": "off",
  "no-unsafe-finally": "off",
  "no-unsafe-negation": "off",
  "no-unsafe-optional-chaining": "off",
  "no-unused-labels": "off",
  "no-unused-private-class-members": "off",
  "no-unused-vars": "off",
  "no-useless-backreference": "off",
  "no-useless-catch": "off",
  "no-useless-escape": "off",
  "no-useless-rename": "off",
  "no-with": "off",
  "react/forward-ref-uses-ref": "off",
  "react/jsx-key": "off",
  "react/jsx-no-duplicate-props": "off",
  "react/jsx-no-target-blank": "off",
  "react/jsx-no-undef": "off",
  "react/jsx-props-no-spread-multi": "off",
  "react/no-children-prop": "off",
  "react/no-danger-with-children": "off",
  "react/no-direct-mutation-state": "off",
  "react/no-find-dom-node": "off",
  "react/no-is-mounted": "off",
  "react/no-render-return-value": "off",
  "react/no-string-refs": "off",
  "react/void-dom-elements-no-children": "off",
  "require-yield": "off",
  "unicorn/no-await-in-promise-methods": "off",
  "unicorn/no-empty-file": "off",
  "unicorn/no-invalid-fetch-options": "off",
  "unicorn/no-invalid-remove-event-listener": "off",
  "unicorn/no-new-array": "off",
  "unicorn/no-single-promise-in-promise-methods": "off",
  "unicorn/no-thenable": "off",
  "unicorn/no-unnecessary-await": "off",
  "unicorn/no-useless-fallback-in-spread": "off",
  "unicorn/no-useless-length-check": "off",
  "unicorn/no-useless-spread": "off",
  "unicorn/prefer-set-size": "off",
  "unicorn/prefer-string-starts-ends-with": "off",
  "use-isnan": "off",
  "valid-typeof": "off",
}
`;
