<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Header -->
    <EmployeeHeader
      :current-user="currentUser"
      :new-leads-count="notificationCounts.newLeads"
      :messages-count="notificationCounts.messages"
      @search="handleGlobalSearch"
      @profile-click="handleProfileClick"
    />

    <div class="flex">
      <!-- Sidebar -->
      <EmployeeSidebar
        :active-menu-item="activeMenuItem"
        @menu-click="handleMenuClick"
      />

      <!-- Main Content Area -->
      <main class="flex-1 min-h-[calc(100vh-80px)]">
        <slot />
      </main>
    </div>

    <!-- Global Loading Overlay -->
    <div
      v-if="isLoading"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-cyan-600"></div>
        <span>Loading...</span>
      </div>
    </div>

    <!-- Toast Notifications -->
    <div
      v-if="notifications.length"
      class="fixed top-4 right-4 space-y-2 z-40"
    >
      <div
        v-for="notification in notifications"
        :key="notification.id"
        :class="notificationClasses(notification.type)"
        class="p-4 rounded-lg shadow-lg max-w-sm"
      >
        <div class="flex items-center justify-between">
          <span>{{ notification.message }}</span>
          <button
            @click="removeNotification(notification.id)"
            class="ml-2 text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import EmployeeHeader from '@/components/employee/EmployeeHeader.vue'
import EmployeeSidebar from '@/components/employee/EmployeeSidebar.vue'
import { useUserStore } from '@/stores/user'
import { useAppStore } from '@/stores/app'

interface User {
  id: string
  name: string
  avatar?: string
}

interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  message: string
}

interface NotificationCounts {
  newLeads: number
  messages: number
}

// Stores
const userStore = useUserStore()
const appStore = useAppStore()
const router = useRouter()

// State
const activeMenuItem = ref('')
const notifications = ref<Notification[]>([])

const notificationCounts = reactive<NotificationCounts>({
  newLeads: 28,
  messages: 3
})

// Computed
const currentUser = computed<User>(() => ({
  id: userStore.user?.id || '1',
  name: userStore.user?.name || 'John Doe',
  avatar: userStore.user?.avatar
}))

const isLoading = computed(() => appStore.loading)

// Methods
const handleGlobalSearch = (query: string) => {
  console.log('Global search:', query)
  // Implement global search logic
  router.push({ name: 'EmployeeSearch', query: { q: query } })
}

const handleProfileClick = (user: User) => {
  console.log('Profile clicked:', user)
  // Show profile menu or navigate to profile page
}

const handleMenuClick = (item: any) => {
  activeMenuItem.value = item.id
  
  if (item.route) {
    router.push(item.route)
  }
}

const addNotification = (notification: Omit<Notification, 'id'>) => {
  const id = Date.now().toString()
  notifications.value.push({ ...notification, id })
  
  // Auto remove after 5 seconds
  setTimeout(() => {
    removeNotification(id)
  }, 5000)
}

const removeNotification = (id: string) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

const notificationClasses = (type: string) => {
  const baseClasses = 'transition-all duration-300'
  const typeClasses = {
    success: 'bg-green-100 text-green-800 border border-green-200',
    error: 'bg-red-100 text-red-800 border border-red-200',
    warning: 'bg-yellow-100 text-yellow-800 border border-yellow-200',
    info: 'bg-blue-100 text-blue-800 border border-blue-200'
  }
  
  return `${baseClasses} ${typeClasses[type as keyof typeof typeClasses]}`
}

// Expose methods for child components
defineExpose({
  addNotification,
  removeNotification
})
</script>
