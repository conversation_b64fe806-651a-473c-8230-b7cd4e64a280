import { OxlintConfig } from './types.js';
/**
 * Resolves the paths of the "extends" section relative to the given config file.
 */
export declare const resolveRelativeExtendsPaths: (config: OxlintConfig) => void;
/**
 * Appends plugins, rules and overrides from the extends configs files to the given config.
 */
export declare const handleExtendsScope: (extendsConfigs: OxlintConfig[], config: OxlintConfig) => void;
/**
 * tries to return the content of the chain "extends" section from the config.
 */
export declare const readExtendsConfigsFromConfig: (config: OxlintConfig) => OxlintConfig[];
