import { createRouter, createWebHistory } from "vue-router";
import type { RouteRecordRaw } from "vue-router";

// Import route modules
import employeeRoutes from "./modules/employee";
import publicRoutes from "./modules/public";

const routes: RouteRecordRaw[] = [
  // Public routes
  ...publicRoutes,

  // Employee routes
  ...employeeRoutes,

  // Catch all 404
  {
    path: "/:pathMatch(.*)*",
    name: "NotFound",
    component: () => import("@/views/NotFoundView.vue"),
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

// Navigation guards
router.beforeEach((to, from, next) => {
  // Add authentication logic here if needed
  console.log("Navigating to:", to.path);
  next();
});

export default router;
