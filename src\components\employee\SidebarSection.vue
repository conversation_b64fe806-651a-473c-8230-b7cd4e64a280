<template>
  <section class="mt-6">
    <h2 class="text-sm leading-none text-sky-950">{{ title }}</h2>
    <div class="shrink-0 self-stretch mt-3 h-px border border-solid border-slate-300"></div>

    <ul class="mt-3.5 space-y-0">
      <li
        v-for="item in filteredItems"
        :key="item.id"
        :class="[
          'cursor-pointer hover:text-cyan-700 transition-colors',
          activeItem === item.id ? 'text-cyan-700 font-bold' : ''
        ]"
        @click="handleItemClick(item)"
      >
        {{ item.label }}
      </li>
    </ul>
  </section>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface MenuItem {
  id: string
  label: string
  route?: string
  action?: string
}

interface Props {
  title: string
  items: MenuItem[]
  activeItem?: string
  searchQuery?: string
}

interface Emits {
  itemClick: [item: MenuItem]
}

const props = withDefaults(defineProps<Props>(), {
  activeItem: '',
  searchQuery: ''
})

const emit = defineEmits<Emits>()

const filteredItems = computed(() => {
  if (!props.searchQuery) return props.items
  
  return props.items.filter(item =>
    item.label.toLowerCase().includes(props.searchQuery.toLowerCase())
  )
})

const handleItemClick = (item: MenuItem) => {
  emit('itemClick', item)
}
</script>
