"use strict";
const rulesByScope = require("./rules-by-scope.cjs");
const eslintConfig = {
  name: "oxlint/eslint",
  rules: rulesByScope.eslintRules
};
const importConfig = {
  name: "oxlint/import",
  rules: rulesByScope.importRules
};
const jestConfig = {
  name: "oxlint/jest",
  rules: rulesByScope.jestRules
};
const jsdocConfig = {
  name: "oxlint/jsdoc",
  rules: rulesByScope.jsdocRules
};
const jsxA11yConfig = {
  name: "oxlint/jsx-a11y",
  rules: rulesByScope.jsxA11yRules
};
const nextjsConfig = {
  name: "oxlint/nextjs",
  rules: rulesByScope.nextjsRules
};
const nodeConfig = {
  name: "oxlint/node",
  rules: rulesByScope.nodeRules
};
const promiseConfig = {
  name: "oxlint/promise",
  rules: rulesByScope.promiseRules
};
const reactConfig = {
  name: "oxlint/react",
  rules: rulesByScope.reactRules
};
const reactHooksConfig = {
  name: "oxlint/react-hooks",
  rules: rulesByScope.reactHooksRules
};
const reactPerfConfig = {
  name: "oxlint/react-perf",
  rules: rulesByScope.reactPerfRules
};
const typescriptConfig = {
  name: "oxlint/typescript",
  rules: rulesByScope.typescriptRules
};
const unicornConfig = {
  name: "oxlint/unicorn",
  rules: rulesByScope.unicornRules
};
const vitestConfig = {
  name: "oxlint/vitest",
  rules: rulesByScope.vitestRules
};
const configByScope = {
  "flat/eslint": eslintConfig,
  "flat/import": importConfig,
  "flat/jest": jestConfig,
  "flat/jsdoc": jsdocConfig,
  "flat/jsx-a11y": jsxA11yConfig,
  "flat/nextjs": nextjsConfig,
  "flat/node": nodeConfig,
  "flat/promise": promiseConfig,
  "flat/react": reactConfig,
  "flat/react-hooks": reactHooksConfig,
  "flat/react-perf": reactPerfConfig,
  "flat/typescript": typescriptConfig,
  "flat/unicorn": unicornConfig,
  "flat/vitest": vitestConfig
};
module.exports = configByScope;
