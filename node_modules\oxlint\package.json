{"name": "ox<PERSON>", "version": "1.1.0", "description": "<PERSON>ter for the JavaScript Oxidation Compiler", "keywords": [], "author": "Boshen and oxc contributors", "license": "MIT", "homepage": "https://oxc.rs", "bugs": "https://github.com/oxc-project/oxc/issues", "repository": {"type": "git", "url": "https://github.com/oxc-project/oxc", "directory": "npm/oxlint"}, "bin": {"oxlint": "bin/oxlint", "oxc_language_server": "bin/oxc_language_server"}, "funding": {"url": "https://github.com/sponsors/Boshen"}, "engines": {"node": ">=8.*"}, "files": ["bin/oxlint", "bin/oxc_language_server", "configuration_schema.json", "README.md"], "optionalDependencies": {"@oxlint/win32-x64": "1.1.0", "@oxlint/win32-arm64": "1.1.0", "@oxlint/linux-x64-gnu": "1.1.0", "@oxlint/linux-arm64-gnu": "1.1.0", "@oxlint/linux-x64-musl": "1.1.0", "@oxlint/linux-arm64-musl": "1.1.0", "@oxlint/darwin-x64": "1.1.0", "@oxlint/darwin-arm64": "1.1.0"}}