<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Incoming Applications</h1>
      <p class="text-gray-600">Review and process new loan applications</p>
    </div>

    <BaseCard>
      <div class="mb-4 flex justify-between items-center">
        <div class="flex space-x-4">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search applications..."
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-500"
          />
          <select
            v-model="statusFilter"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-500"
          >
            <option value="">All Status</option>
            <option value="pending">Pending</option>
            <option value="review">Under Review</option>
            <option value="approved">Approved</option>
          </select>
        </div>
        <BaseButton @click="refreshData">
          Refresh
        </BaseButton>
      </div>

      <BaseTable
        :columns="columns"
        :data="filteredApplications"
        :show-header="true"
      >
        <template #cell-status="{ value }">
          <span
            :class="getStatusClass(value)"
            class="px-2 py-1 rounded-full text-xs font-medium"
          >
            {{ value }}
          </span>
        </template>
        
        <template #cell-actions="{ item }">
          <div class="flex space-x-2">
            <BaseButton size="sm" @click="viewApplication(item)">
              View
            </BaseButton>
            <BaseButton size="sm" variant="secondary" @click="processApplication(item)">
              Process
            </BaseButton>
          </div>
        </template>
      </BaseTable>
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { BaseCard, BaseTable, BaseButton } from '@/components/common'

interface Application {
  id: string
  customerName: string
  amount: number
  status: string
  submittedAt: string
  provider: string
}

const searchQuery = ref('')
const statusFilter = ref('')

const columns = [
  { key: 'id', title: 'Application ID' },
  { key: 'customerName', title: 'Customer Name' },
  { key: 'amount', title: 'Loan Amount' },
  { key: 'provider', title: 'Lead Provider' },
  { key: 'status', title: 'Status' },
  { key: 'submittedAt', title: 'Submitted' },
  { key: 'actions', title: 'Actions' }
]

const applications = ref<Application[]>([
  {
    id: 'APP001',
    customerName: 'John Smith',
    amount: 5000,
    status: 'pending',
    submittedAt: '2025-06-30',
    provider: 'RP232'
  },
  {
    id: 'APP002',
    customerName: 'Jane Doe',
    amount: 3000,
    status: 'review',
    submittedAt: '2025-06-30',
    provider: 'LP445'
  }
])

const filteredApplications = computed(() => {
  let filtered = applications.value

  if (searchQuery.value) {
    filtered = filtered.filter(app =>
      app.customerName.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      app.id.toLowerCase().includes(searchQuery.value.toLowerCase())
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(app => app.status === statusFilter.value)
  }

  return filtered
})

const getStatusClass = (status: string) => {
  const classes = {
    pending: 'bg-yellow-100 text-yellow-800',
    review: 'bg-blue-100 text-blue-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const refreshData = () => {
  console.log('Refreshing applications data...')
}

const viewApplication = (application: Application) => {
  console.log('Viewing application:', application.id)
}

const processApplication = (application: Application) => {
  console.log('Processing application:', application.id)
}
</script>
