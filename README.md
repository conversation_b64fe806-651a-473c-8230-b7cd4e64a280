# Monarch UI - Vue 3 Project

Dự án Vue 3 với TypeScript được tổ chức theo cấu trúc thư mục rõ ràng và dễ bảo trì.

## Cấu trúc thư mục

```
src/
├── components/          # Các Vue components tái sử dụng
├── decorators/          # Decorators cho TypeScript
├── hooks/              # Composition API hooks
├── i18n/               # Cấu hình đa ngôn ngữ
│   └── locales/        # Các file ngôn ngữ (en.json, vi.json)
├── layouts/            # Layout components
├── plugins/            # Vue plugins và cấu hình
├── router/             # Vue Router cấu hình
├── stores/             # Pinia stores (state management)
├── styles/             # CSS/SCSS files
├── types/              # TypeScript type definitions
├── views/              # Page components
├── App.vue             # Root component
├── components.d.ts     # Auto-generated component types
└── main.ts             # Entry point
```

## Tính năng

- ✅ Vue 3 với Composition API
- ✅ TypeScript
- ✅ Vue Router 4
- ✅ Pinia (State Management)
- ✅ Vue I18n (Internationalization)
- ✅ CSS Variables & Utility Classes
- ✅ Composable Hooks
- ✅ TypeScript Decorators
- ✅ Modular Architecture

## Cài đặt và chạy

```sh
# Cài đặt dependencies
npm install

# Chạy development server
npm run dev

# Build cho production
npm run build

# Lint code
npm run lint
```

## Hướng dẫn sử dụng

### 1. Components

Tạo components trong thư mục `src/components/`

### 2. Views/Pages

Tạo pages trong thư mục `src/views/`

### 3. State Management

Sử dụng Pinia stores trong `src/stores/`

### 4. Routing

Cấu hình routes trong `src/router/index.ts`

### 5. Styling

- CSS variables: `src/styles/variables.css`
- Component styles: `src/styles/components.css`
- Utility classes: `src/styles/utilities.css`

### 6. Internationalization

Thêm translations trong `src/i18n/locales/`

### 7. Hooks

Tạo reusable logic trong `src/hooks/`

### 8. Types

Định nghĩa TypeScript types trong `src/types/`
