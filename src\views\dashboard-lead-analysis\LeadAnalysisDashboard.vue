<template>
  <div class="overflow-hidden bg-gray-100">
    <DashboardHeader />

    <div class="w-full max-md:max-w-full">
      <div class="flex gap-5 max-md:flex-col max-md:">
        <DashboardSidebar />

        <main class="ml-5 w-[83%] max-md:ml-0 max-md:w-full">
          <div
            class="self-stretch my-auto w-full max-md:mt-10 max-md:max-w-full"
          >
            <p>xin chao</p>
            <DashboardTabs />

            <WelcomeSection />

            <TopLeadProviders />

            <!-- Temporarily comment out StatsGrid to isolate the issue -->
            <!-- <StatsGrid /> -->

            <TopLeadsComparison />

            <LeadStatusTable />
          </div>
        </main>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import DashboardHeader from "@/views/dashboard-lead-analysis/DashboardHeader.vue";
import DashboardSidebar from "@/views/dashboard-lead-analysis/DashboardSidebar.vue";
import DashboardTabs from "@/views/dashboard-lead-analysis/DashboardTabs.vue";
import WelcomeSection from "@/views/dashboard-lead-analysis/WelcomeSection.vue";
import TopLeadProviders from "@/views/dashboard-lead-analysis/TopLeadProviders.vue";
import StatsGrid from "@/views/dashboard-lead-analysis/StatsGrid.vue";
import TopLeadsComparison from "@/views/dashboard-lead-analysis/TopLeadsComparison.vue";
import LeadStatusTable from "@/views/dashboard-lead-analysis/LeadStatusTable.vue";
</script>
