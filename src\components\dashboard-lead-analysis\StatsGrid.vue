<template>
  <div class="mx-3.5 mt-3.5 max-md:mr-2.5 max-md:max-w-full">
    <div class="flex gap-5 max-md:flex-col max-md:">
      <!-- Applications Card -->
      <div class="w-3/12 max-md:ml-0 max-md:w-full">
        <article
          class="grow pt-3.5 pb-6 w-full bg-white rounded-2xl border-t-0 border-slate-300 max-md:mt-4"
        >
          <div class="flex flex-col items-start px-3.5 w-full">
            <h3 class="text-base font-bold leading-none text-teal-950">
              Applications
            </h3>
            <div class="mt-2 text-3xl font-bold leading-none text-teal-950">
              30
            </div>
            <p class="mt-2 text-xs leading-loose text-slate-500">Today</p>

            <div class="mt-14 text-sm leading-none text-teal-950 max-md:mt-10">
              Non Organic
            </div>
            <div class="flex self-stretch mt-3.5">
              <div
                class="grow shrink-0 my-auto mr-0 bg-violet-100 rounded-md basis-0 w-fit"
              >
                <div class="flex shrink-0 h-2.5 bg-cyan-600 rounded-md"></div>
              </div>
              <div
                class="flex shrink-0 bg-white rounded-full border-cyan-600 border-solid border-[6px] h-[22px] w-[22px]"
              ></div>
            </div>
            <div
              class="self-end mt-2 mr-6 text-xs font-bold leading-loose text-teal-950 max-md:mr-2.5"
            >
              27
            </div>
          </div>

          <div class="flex flex-col items-start px-3.5 mt-6">
            <div class="text-sm leading-none text-teal-950">Organic</div>
            <div
              class="flex z-10 shrink-0 mt-3.5 ml-3 bg-white rounded-full border-cyan-600 border-solid border-[6px] h-[22px] w-[22px] max-md:ml-2.5"
            ></div>
            <div
              class="flex flex-col items-start self-stretch bg-violet-100 rounded-md max-md:pr-5"
            >
              <div class="flex shrink-0 w-7 h-2.5 bg-cyan-600 rounded-md"></div>
            </div>
            <div
              class="mt-3.5 ml-5 text-xs font-bold leading-loose text-teal-950 max-md:ml-2.5"
            >
              3
            </div>
          </div>
        </article>
      </div>

      <!-- Conversion Rate Card -->
      <div class="ml-5 w-3/12 max-md:ml-0 max-md:w-full">
        <article
          class="flex flex-col items-start py-4 pr-16 pl-3.5 mx-auto w-full bg-white rounded-2xl border-t-0 border-slate-300 max-md:pr-5 max-md:mt-4"
        >
          <h3 class="text-base font-bold leading-none text-teal-950">
            Conversion Rate
          </h3>
          <p class="text-xs leading-loose text-slate-500">Today</p>

          <div
            class="flex relative flex-col items-end self-end pl-10 mt-6 max-w-full rounded-full aspect-[0.844] w-[189px] max-md:pl-5"
          >
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/1746871fc892a45c03fac8239855f5219ccf9582?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
              class="object-cover absolute inset-0 size-full"
              alt="Chart background"
            />
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/0eb6eafb539b04a0fa7bb84ee3804e89a8a737fa?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
              class="object-contain mr-0 w-28 max-w-full rounded-full aspect-square"
              alt="Chart segment 1"
            />
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/dbba5e405e007886c35043caa115f391c0a397ec?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
              class="object-contain mr-0 w-28 max-w-full rounded-full aspect-square"
              alt="Chart segment 2"
            />
          </div>

          <div class="flex gap-2 mt-6 text-xs text-teal-950 max-md:ml-2">
            <div
              class="flex shrink-0 my-auto bg-cyan-600 rounded-full h-[9px] w-[9px]"
            ></div>
            <span>Converted</span>
            <div
              class="flex shrink-0 my-auto rounded-full bg-teal-950 h-[9px] w-[9px]"
            ></div>
            <span>Not Converted</span>
          </div>
        </article>
      </div>

      <!-- Stats Cards Grid -->
      <div class="ml-5 w-[51%] max-md:ml-0 max-md:w-full">
        <div class="w-full max-md:mt-4 max-md:max-w-full">
          <!-- First Row -->
          <div class="w-full max-md:max-w-full">
            <div class="flex gap-5 max-md:flex-col max-md:">
              <div class="w-6/12 max-md:ml-0 max-md:w-full">
                <StatsCard
                  :value="236"
                  title="Total Accepted Loans"
                  :percentage="18"
                  change-value="+32"
                  trend-direction="up"
                />
              </div>
              <div class="ml-5 w-6/12 max-md:ml-0 max-md:w-full">
                <StatsCard
                  :value="35"
                  title="Total Denied Loans"
                  :percentage="2"
                  change-value="+3"
                  trend-direction="up"
                />
              </div>
            </div>
          </div>

          <!-- Second Row -->
          <div class="mt-3.5 w-full max-md:max-w-full">
            <div class="flex gap-5 max-md:flex-col max-md:">
              <div class="w-6/12 max-md:ml-0 max-md:w-full">
                <StatsCard
                  :value="66"
                  title="Total of E-sig w/in 20 min."
                  :percentage="10"
                  change-value="-22"
                  trend-direction="down"
                />
              </div>
              <div class="ml-5 w-6/12 max-md:ml-0 max-md:w-full">
                <StatsCard
                  :value="126"
                  title="Total Pending Loans"
                  :percentage="31"
                  change-value="+11"
                  trend-direction="up"
                />
              </div>
            </div>
          </div>

          <!-- Third Row -->
          <div class="mt-3.5 w-full max-md:max-w-full">
            <div class="flex gap-5 max-md:flex-col max-md:">
              <div class="w-6/12 max-md:ml-0 max-md:w-full">
                <StatsCard
                  :value="44"
                  title="Voided & Withdrawn"
                  :percentage="3"
                  change-value="-5"
                  trend-direction="down"
                />
              </div>
              <div class="ml-5 w-6/12 max-md:ml-0 max-md:w-full">
                <StatsCard
                  :value="272"
                  title="Total Originated"
                  :percentage="46"
                  change-value="+32"
                  trend-direction="up"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import StatsCard from "./StatsCard.vue";
</script>
