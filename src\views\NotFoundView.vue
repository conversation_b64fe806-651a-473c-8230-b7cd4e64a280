<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-100">
    <div class="text-center">
      <h1 class="text-6xl font-bold text-gray-900 mb-4">404</h1>
      <h2 class="text-2xl font-semibold text-gray-700 mb-4">Page Not Found</h2>
      <p class="text-gray-600 mb-8">
        The page you are looking for doesn't exist or has been moved.
      </p>
      <div class="space-x-4">
        <BaseButton @click="goBack" variant="outline">
          Go Back
        </BaseButton>
        <BaseButton @click="goHome" variant="primary">
          Go Home
        </BaseButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { BaseButton } from '@/components/common'

const router = useRouter()

const goBack = () => {
  router.go(-1)
}

const goHome = () => {
  router.push('/')
}
</script>
