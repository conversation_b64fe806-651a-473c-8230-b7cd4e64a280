import { vitestRules, unicornRules, typescriptRules, reactPerfRules, reactHooksRules, reactRules, promiseRules, nodeRules, nextjsRules, jsxA11yRules, jsdocRules, jestRules, importRules, eslintRules } from "./rules-by-scope.mjs";
const eslintConfig = {
  name: "oxlint/eslint",
  rules: eslintRules
};
const importConfig = {
  name: "oxlint/import",
  rules: importRules
};
const jestConfig = {
  name: "oxlint/jest",
  rules: jestRules
};
const jsdocConfig = {
  name: "oxlint/jsdoc",
  rules: jsdocRules
};
const jsxA11yConfig = {
  name: "oxlint/jsx-a11y",
  rules: jsxA11yRules
};
const nextjsConfig = {
  name: "oxlint/nextjs",
  rules: nextjsRules
};
const nodeConfig = {
  name: "oxlint/node",
  rules: nodeRules
};
const promiseConfig = {
  name: "oxlint/promise",
  rules: promiseRules
};
const reactConfig = {
  name: "oxlint/react",
  rules: reactRules
};
const reactHooksConfig = {
  name: "oxlint/react-hooks",
  rules: reactHooksRules
};
const reactPerfConfig = {
  name: "oxlint/react-perf",
  rules: reactPerfRules
};
const typescriptConfig = {
  name: "oxlint/typescript",
  rules: typescriptRules
};
const unicornConfig = {
  name: "oxlint/unicorn",
  rules: unicornRules
};
const vitestConfig = {
  name: "oxlint/vitest",
  rules: vitestRules
};
const configByScope = {
  "flat/eslint": eslintConfig,
  "flat/import": importConfig,
  "flat/jest": jestConfig,
  "flat/jsdoc": jsdocConfig,
  "flat/jsx-a11y": jsxA11yConfig,
  "flat/nextjs": nextjsConfig,
  "flat/node": nodeConfig,
  "flat/promise": promiseConfig,
  "flat/react": reactConfig,
  "flat/react-hooks": reactHooksConfig,
  "flat/react-perf": reactPerfConfig,
  "flat/typescript": typescriptConfig,
  "flat/unicorn": unicornConfig,
  "flat/vitest": vitestConfig
};
export {
  configByScope as default
};
