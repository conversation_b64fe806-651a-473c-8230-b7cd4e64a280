<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Search Results</h1>
      <p class="text-gray-600">Search results for: "{{ searchQuery }}"</p>
    </div>

    <BaseCard>
      <div class="text-center py-8">
        <h3 class="text-lg font-medium text-gray-900 mb-2">Search Results</h3>
        <p class="text-gray-600">This feature is under development</p>
      </div>
    </BaseCard>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { BaseCard } from '@/components/common'

const route = useRoute()

const searchQuery = computed(() => route.query.q as string || '')
</script>
