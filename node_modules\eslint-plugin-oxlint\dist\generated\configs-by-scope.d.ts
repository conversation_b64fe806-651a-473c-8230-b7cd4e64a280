declare const configByScope: {
    'flat/eslint': {
        name: string;
        rules: Record<string, "off">;
    };
    'flat/import': {
        name: string;
        rules: Record<string, "off">;
    };
    'flat/jest': {
        name: string;
        rules: Record<string, "off">;
    };
    'flat/jsdoc': {
        name: string;
        rules: Record<string, "off">;
    };
    'flat/jsx-a11y': {
        name: string;
        rules: Record<string, "off">;
    };
    'flat/nextjs': {
        name: string;
        rules: Record<string, "off">;
    };
    'flat/node': {
        name: string;
        rules: Record<string, "off">;
    };
    'flat/promise': {
        name: string;
        rules: Record<string, "off">;
    };
    'flat/react': {
        name: string;
        rules: Record<string, "off">;
    };
    'flat/react-hooks': {
        name: string;
        rules: Record<string, "off">;
    };
    'flat/react-perf': {
        name: string;
        rules: Record<string, "off">;
    };
    'flat/typescript': {
        name: string;
        rules: Record<string, "off">;
    };
    'flat/unicorn': {
        name: string;
        rules: Record<string, "off">;
    };
    'flat/vitest': {
        name: string;
        rules: Record<string, "off">;
    };
};
export default configByScope;
