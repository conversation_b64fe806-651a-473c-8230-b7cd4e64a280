"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const constants = require("../constants.cjs");
const configsByCategory = require("../generated/configs-by-category.cjs");
const utilities = require("./utilities.cjs");
const allRulesObjects = Object.values(configsByCategory).map(
  (config) => config.rules
);
const allRules = allRulesObjects.flatMap(
  (rulesObject) => Object.keys(rulesObject)
);
const getEsLintRuleName = (rule) => {
  if (!rule.includes("/")) {
    return allRules.find(
      (search) => search.endsWith(`/${rule}`) || search === rule
    );
  }
  const match = rule.match(/(^.*)\/(.*)/);
  if (match === null) {
    return void 0;
  }
  const pluginName = match[1];
  const ruleName = match[2];
  let esPluginName = pluginName in constants.aliasPluginNames ? constants.aliasPluginNames[pluginName] : pluginName;
  if (esPluginName === "react" && constants.reactHookRulesInsideReactScope.includes(ruleName)) {
    esPluginName = "react-hooks";
  }
  const expectedRule = esPluginName === "" ? ruleName : `${esPluginName}/${ruleName}`;
  return allRules.find((rule2) => rule2 == expectedRule);
};
const isValueInSet = (value, validSet) => validSet.includes(value) || Array.isArray(value) && validSet.includes(value[0]);
const isDeactivateValue = (value) => isValueInSet(value, ["off", 0]);
const isActiveValue = (value) => isValueInSet(value, ["error", "warn", 1, 2]);
const handleRulesScope = (oxlintRules, rules) => {
  for (const rule in oxlintRules) {
    const eslintName = getEsLintRuleName(rule);
    if (eslintName === void 0) {
      continue;
    }
    if (isActiveValue(oxlintRules[rule])) {
      rules[eslintName] = "off";
    } else if (rule in rules && isDeactivateValue(oxlintRules[rule])) {
      delete rules[eslintName];
    }
  }
};
const readRulesFromConfig = (config) => {
  return "rules" in config && utilities.isObject(config.rules) ? config.rules : void 0;
};
exports.handleRulesScope = handleRulesScope;
exports.readRulesFromConfig = readRulesFromConfig;
