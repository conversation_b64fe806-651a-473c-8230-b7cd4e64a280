<template>
  <div class="py-3 pr-6 pl-2 w-full text-white border-t-0 bg-teal-950 border-slate-300 max-md:pr-5 max-md:max-w-full">
    <div class="flex flex-wrap gap-10 w-full max-md:max-w-full">
      <div class="flex flex-1 gap-6 my-auto">
        <h2 class="grow my-auto text-sm font-bold leading-none">
          My Contact Info
        </h2>

        <div class="flex flex-auto gap-3 text-xs font-medium leading-loose whitespace-nowrap">
          <img
            src="https://cdn.builder.io/api/v1/image/assets/TEMP/680ec530bc1dd0a183574a7c2d29cd8bc1ecde55?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
            class="object-contain shrink-0 w-14 rounded-lg aspect-[2.33]"
            alt="Contact info icon"
          />
          <div class="flex gap-2.5 px-2 py-0.5 bg-cyan-600 rounded-lg">
            <span class="basis-auto"><EMAIL></span>
          </div>
        </div>
      </div>

      <div class="flex flex-1 gap-3 items-center text-sm font-medium leading-none">
        <span class="self-stretch my-auto">Auto Refresh</span>
        <div class="shrink-0 self-stretch my-auto w-px h-4 border border-solid border-slate-300"></div>
        <span class="self-stretch my-auto">All Stores</span>
        <div class="shrink-0 self-stretch my-auto w-px h-4 border border-solid border-slate-300"></div>
        <span class="self-stretch my-auto">Filters (0)</span>
      </div>
    </div>

    <nav class="flex mt-2 max-w-full text-xs leading-loose w-[822px]">
      <div class="flex shrink-0 h-6 bg-cyan-600 rounded-lg w-[88px]"></div>
      <div class="flex flex-wrap flex-auto gap-2.5 items-center self-start min-h-5">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          :class="[
            'self-stretch my-auto transition-colors',
            activeTab === tab.id ? 'font-semibold text-cyan-600' : 'text-white hover:text-cyan-300'
          ]"
          @click="handleTabClick(tab.id)"
        >
          {{ tab.label }}
        </button>
      </div>
    </nav>
  </div>
</template>

<script setup lang="ts">
interface Tab {
  id: string
  label: string
}

interface Props {
  activeTab: string
  tabs: Tab[]
}

interface Emits {
  tabChange: [tabId: string]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleTabClick = (tabId: string) => {
  emit('tabChange', tabId)
}
</script>
