<template>
  <BaseCard :custom-class="cardClass">
    <div class="flex gap-3.5">
      <div class="grow my-auto text-3xl font-bold leading-none text-center text-teal-950">
        {{ formattedValue }}
      </div>

      <div class="flex flex-col items-start">
        <div
          v-if="showTrend"
          :class="trendBadgeClasses"
        >
          <component
            :is="trendIcon"
            class="w-1.5 h-1.5"
          />
          <span>{{ percentage }}%</span>
        </div>

        <h3 class="text-base font-bold leading-none text-teal-950">
          {{ title }}
        </h3>

        <p class="text-sm leading-loose text-slate-600">
          <span :class="changeValueClasses">{{ changeValue }}</span>
          vs previous period
        </p>
      </div>
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
import BaseCard from './BaseCard.vue'

interface Props {
  value: string | number
  title: string
  percentage?: string | number
  changeValue: string
  trendDirection?: 'up' | 'down' | 'neutral'
  showTrend?: boolean
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  percentage: 0,
  trendDirection: 'neutral',
  showTrend: true,
  customClass: ''
})

const formattedValue = computed(() => {
  if (typeof props.value === 'number') {
    return props.value.toLocaleString()
  }
  return props.value
})

const cardClass = computed(() => {
  return `${props.customClass} max-md:mt-3.5`
})

const trendBadgeClasses = computed(() => {
  const baseClasses = 'flex gap-1 self-end px-2 py-2 text-xs font-semibold leading-loose text-center text-white whitespace-nowrap rounded-[999px]'
  
  const trendClasses = {
    up: 'bg-green-600',
    down: 'bg-red-600',
    neutral: 'bg-cyan-600'
  }
  
  return `${baseClasses} ${trendClasses[props.trendDirection]}`
})

const changeValueClasses = computed(() => {
  const baseClasses = 'font-bold'
  
  const colorClasses = {
    up: 'text-green-600',
    down: 'text-red-600',
    neutral: 'text-blue-600'
  }
  
  return `${baseClasses} ${colorClasses[props.trendDirection]}`
})

const trendIcon = computed(() => {
  // Using simple text icons for now, can be replaced with actual icon components
  return props.trendDirection === 'up' ? '↗' : props.trendDirection === 'down' ? '↘' : '→'
})
</script>
