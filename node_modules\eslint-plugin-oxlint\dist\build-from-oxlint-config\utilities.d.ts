import { OxlintConfig } from './types.js';
/**
 * Detects it the value is an object
 */
export declare const isObject: (value: unknown) => boolean;
/**
 * tries to read the oxlint config file and returning its JSON content.
 * if the file is not found or could not be parsed, undefined is returned.
 * And an error message will be emitted to `console.error`
 */
export declare const getConfigContent: (oxlintConfigFile: string) => OxlintConfig | undefined;
