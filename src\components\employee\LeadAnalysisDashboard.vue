<template>
  <div class="flex flex-col min-h-screen bg-gray-100">
    <!-- Header -->
    <EmployeeHeader
      :current-user="currentUser"
      :new-leads-count="dashboardData.newLeadsCount"
      :messages-count="dashboardData.messagesCount"
      @search="handleSearch"
      @profile-click="handleProfileClick"
    />

    <div class="flex flex-1">
      <!-- Sidebar -->
      <EmployeeSidebar
        :active-menu-item="activeMenuItem"
        @menu-click="handleMenuClick"
      />

      <!-- Main Content -->
      <main class="flex-1 p-6">
        <!-- Dashboard Tabs -->
        <DashboardTabs
          :active-tab="activeTab"
          :tabs="dashboardTabs"
          @tab-change="handleTabChange"
        />

        <!-- Welcome Section -->
        <WelcomeSection
          :user-name="currentUser.name"
          :time-period="selectedTimePeriod"
          @time-period-change="handleTimePeriodChange"
        />

        <!-- Dashboard Content -->
        <div class="space-y-6">
          <!-- Top Lead Providers -->
          <TopLeadProviders :providers="dashboardData.topProviders" />

          <!-- Stats Grid -->
          <StatsGrid :stats="dashboardData.stats" />

          <!-- Top Leads Comparison -->
          <TopLeadsComparison :comparison-data="dashboardData.leadsComparison" />

          <!-- Lead Status Table -->
          <LeadStatusTable :leads="dashboardData.leads" />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import EmployeeHeader from './EmployeeHeader.vue'
import EmployeeSidebar from './EmployeeSidebar.vue'
import DashboardTabs from './DashboardTabs.vue'
import WelcomeSection from './WelcomeSection.vue'
import TopLeadProviders from './TopLeadProviders.vue'
import StatsGrid from './StatsGrid.vue'
import TopLeadsComparison from './TopLeadsComparison.vue'
import LeadStatusTable from './LeadStatusTable.vue'

interface User {
  id: string
  name: string
  avatar?: string
}

interface DashboardData {
  newLeadsCount: number
  messagesCount: number
  topProviders: any[]
  stats: any[]
  leadsComparison: any[]
  leads: any[]
}

// Props
interface Props {
  userId?: string
}

const props = defineProps<Props>()

// State
const currentUser = ref<User>({
  id: props.userId || '1',
  name: 'John Doe',
  avatar: 'https://cdn.builder.io/api/v1/image/assets/TEMP/6e660936d2efcdbfb8b39739e13236f08ab20a85?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde'
})

const activeMenuItem = ref('lead-analysis')
const activeTab = ref('lead-analysis')
const selectedTimePeriod = ref('today')

const dashboardTabs = [
  { id: 'lead-analysis', label: 'Lead Analysis' },
  { id: 'new-loan-summary', label: 'New Loan Summary' },
  { id: 'cpa-lead-provider', label: 'CPA by Lead Provider' },
  { id: 'promise-to-pay', label: 'Promise to Pay Summary' },
  { id: 'collection-age', label: 'Collection Age Bucket' },
  { id: 'defaulted-payments', label: 'Defaulted vs. Payments Report' }
]

const dashboardData = reactive<DashboardData>({
  newLeadsCount: 28,
  messagesCount: 3,
  topProviders: [],
  stats: [],
  leadsComparison: [],
  leads: []
})

// Event Handlers
const handleSearch = (query: string) => {
  console.log('Search:', query)
  // Implement search logic
}

const handleProfileClick = (user: User) => {
  console.log('Profile clicked:', user)
  // Implement profile menu logic
}

const handleMenuClick = (item: any) => {
  activeMenuItem.value = item.id
  // Navigate to route if available
  if (item.route) {
    // Use router.push(item.route) when router is available
    console.log('Navigate to:', item.route)
  }
}

const handleTabChange = (tabId: string) => {
  activeTab.value = tabId
  // Load data for the selected tab
  loadTabData(tabId)
}

const handleTimePeriodChange = (period: string) => {
  selectedTimePeriod.value = period
  // Reload dashboard data for the selected period
  loadDashboardData()
}

// Data Loading
const loadDashboardData = () => {
  // Implement data loading logic
  console.log('Loading dashboard data for period:', selectedTimePeriod.value)
}

const loadTabData = (tabId: string) => {
  // Implement tab-specific data loading
  console.log('Loading data for tab:', tabId)
}

// Initialize
loadDashboardData()
</script>
