<template>
  <BaseCard :padding="'none'" :custom-class="customClass">
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead v-if="showHeader" class="bg-gray-50">
          <tr>
            <th
              v-for="column in columns"
              :key="column.key"
              :class="headerCellClasses"
            >
              {{ column.title }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(item, index) in data"
            :key="getRowKey(item, index)"
            :class="rowClasses"
          >
            <td
              v-for="column in columns"
              :key="column.key"
              :class="cellClasses"
            >
              <slot
                :name="`cell-${column.key}`"
                :item="item"
                :value="getColumnValue(item, column.key)"
                :index="index"
              >
                {{ getColumnValue(item, column.key) }}
              </slot>
            </td>
          </tr>
        </tbody>
      </table>
      
      <div v-if="!data.length" class="text-center py-8 text-gray-500">
        <slot name="empty">
          No data available
        </slot>
      </div>
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
import BaseCard from './BaseCard.vue'

interface Column {
  key: string
  title: string
  width?: string
}

interface Props {
  columns: Column[]
  data: Record<string, any>[]
  showHeader?: boolean
  rowKey?: string
  customClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  showHeader: true,
  rowKey: 'id',
  customClass: ''
})

const headerCellClasses = 'px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'
const cellClasses = 'px-4 py-3 text-sm text-gray-900'
const rowClasses = 'border-b border-gray-200 hover:bg-gray-50'

const getRowKey = (item: Record<string, any>, index: number): string | number => {
  return item[props.rowKey] || index
}

const getColumnValue = (item: Record<string, any>, key: string): any => {
  return key.split('.').reduce((obj, k) => obj?.[k], item)
}
</script>
