<template>
  <section
    class="flex flex-wrap gap-5 justify-between mt-7 mr-3.5 ml-6 w-full max-w-[1295px] text-teal-950 max-md:mr-2.5 max-md:max-w-full"
  >
    <h1 class="my-auto text-4xl font-bold leading-none">Welcome Back, <PERSON>.</h1>

    <div class="flex flex-wrap gap-3.5 text-sm">
      <label class="grow my-auto leading-none"> Visualized Time Period: </label>

      <div
        class="flex flex-auto gap-10 px-4 py-3.5 leading-none whitespace-nowrap bg-white rounded-xl border border-solid border-slate-300"
      >
        <span>Today</span>
        <img
          src="https://cdn.builder.io/api/v1/image/assets/TEMP/cf8ff81a55270f6f3ed25614069ef65b66aa8a83?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
          class="object-contain shrink-0 self-start aspect-[1.13] w-[17px]"
          alt="Dropdown arrow"
        />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// No props needed for this component
</script>
