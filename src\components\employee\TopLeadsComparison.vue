<template>
  <BaseCard custom-class="mx-3.5 mt-7 p-6">
    <h2 class="text-lg font-bold text-teal-950 mb-4">Top Leads Comparison</h2>
    <div class="text-center py-8">
      <p class="text-gray-600">Top leads comparison chart will be displayed here</p>
    </div>
  </BaseCard>
</template>

<script setup lang="ts">
import { BaseCard } from '@/components/common'

interface Props {
  comparisonData?: any[]
}

const props = defineProps<Props>()
</script>
