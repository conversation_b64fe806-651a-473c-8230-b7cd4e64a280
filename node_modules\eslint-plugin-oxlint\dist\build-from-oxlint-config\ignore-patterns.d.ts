import { EslintPluginOxlintConfig, OxlintConfig, OxlintConfigIgnorePatterns } from './types.js';
/**
 * adds the ignore patterns to the config
 */
export declare const handleIgnorePatternsScope: (ignorePatterns: OxlintConfigIgnorePatterns, config: EslintPluginOxlintConfig) => void;
/**
 * tries to return the "ignorePattern" section from the config.
 * it returns `undefined` when not found or invalid.
 */
export declare const readIgnorePatternsFromConfig: (config: OxlintConfig) => OxlintConfigIgnorePatterns | undefined;
