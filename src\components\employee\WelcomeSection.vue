<template>
  <section class="flex flex-wrap gap-5 justify-between mt-7 mr-3.5 ml-6 w-full max-w-[1295px] text-teal-950 max-md:mr-2.5 max-md:max-w-full">
    <h1 class="my-auto text-4xl font-bold leading-none">Welcome Back, {{ userName }}.</h1>

    <div class="flex flex-wrap gap-3.5 text-sm">
      <label class="grow my-auto leading-none">Visualized Time Period:</label>

      <div class="flex flex-auto gap-10 px-4 py-3.5 leading-none whitespace-nowrap bg-white rounded-xl border border-solid border-slate-300">
        <select
          :value="timePeriod"
          @change="handleTimePeriodChange"
          class="bg-transparent outline-none"
        >
          <option value="today">Today</option>
          <option value="week">This Week</option>
          <option value="month">This Month</option>
          <option value="quarter">This Quarter</option>
          <option value="year">This Year</option>
        </select>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
interface Props {
  userName: string
  timePeriod: string
}

interface Emits {
  timePeriodChange: [period: string]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleTimePeriodChange = (event: Event) => {
  const target = event.target as HTMLSelectElement
  emit('timePeriodChange', target.value)
}
</script>
