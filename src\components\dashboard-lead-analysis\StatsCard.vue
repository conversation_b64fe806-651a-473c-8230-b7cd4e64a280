<template>
  <article
    class="flex grow gap-3.5 px-3.5 pt-3.5 pb-8 w-full bg-white rounded-2xl border-t-0 border-slate-300 text-teal-950 max-md:mt-3.5"
  >
    <div class="grow my-auto text-3xl font-bold leading-none text-center">
      {{ value }}
    </div>

    <div class="flex flex-col items-start">
      <div
        class="flex gap-1 self-end px-2 py-2 text-xs font-semibold leading-loose text-center text-white whitespace-nowrap bg-cyan-600 rounded-[999px]"
      >
        <img
          :src="trendIcon"
          class="object-contain shrink-0 self-start w-1.5 aspect-square"
          :alt="trendDirection + ' trend'"
        />
        <span>{{ percentage }}%</span>
      </div>

      <h3 class="text-base font-bold leading-none">
        {{ title }}
      </h3>

      <p class="text-sm leading-loose">
        <span class="font-bold text-blue-600">{{ changeValue }}</span>
        vs previous period
      </p>
    </div>
  </article>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  value: string | number;
  title: string;
  percentage: string | number;
  changeValue: string;
  trendDirection: "up" | "down";
}

const props = defineProps<Props>();

const trendIcon = computed(() => {
  return props.trendDirection === "up"
    ? "https://cdn.builder.io/api/v1/image/assets/TEMP/abbd3018468ae1ccaeffa3680c0095aff03118d8?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
    : "https://cdn.builder.io/api/v1/image/assets/TEMP/2aae7a1ba269051f583efd30cabc6d672c01ee62?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde";
});
</script>
