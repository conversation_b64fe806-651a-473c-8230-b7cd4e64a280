<template>
  <section class="flex gap-5 justify-between items-end px-3.5 py-3.5 mx-3.5 mt-7 bg-cyan-600 rounded-2xl border-t-0 border-slate-300 max-md:mr-2.5 max-md:max-w-full">
    <div class="flex flex-col items-start self-stretch">
      <h2 class="text-base font-bold leading-none text-white">
        Top 5 Lead Providers
      </h2>
      <p class="text-xs leading-loose text-white">Origination Rate (%)</p>

      <div class="flex gap-5 px-3 py-3 mt-3 ml-4 bg-teal-950 bg-opacity-30 rounded-[999px] max-md:ml-2.5">
        <div class="w-[70px] h-[70px] bg-gray-300 rounded-full flex items-center justify-center">
          <span class="text-white font-bold">RP</span>
        </div>
        <div class="flex flex-col items-start">
          <p class="text-sm leading-loose text-white">RP232</p>
          <div class="flex gap-2 self-stretch mt-2 whitespace-nowrap">
            <span class="grow text-3xl font-bold leading-none text-white">91%</span>
            <span class="self-start px-2.5 text-xs font-semibold leading-5 text-center text-cyan-600 bg-violet-100 rounded-[999px]">
              46/51
            </span>
          </div>
          <p class="mt-2 text-xs font-bold leading-6 text-white">$3k profit</p>
        </div>
      </div>
    </div>

    <div class="w-[70px] h-[70px] bg-gray-300 rounded-full flex items-center justify-center">
      <span class="text-white font-bold">LP</span>
    </div>
  </section>
</template>

<script setup lang="ts">
interface Props {
  providers?: any[]
}

const props = defineProps<Props>()
</script>
