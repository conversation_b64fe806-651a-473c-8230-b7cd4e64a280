<template>
  <div class="overflow-hidden bg-gray-100">
    <DashboardHeader />

    <div class="w-full max-md:max-w-full">
      <div class="flex gap-5 max-md:flex-col max-md:">
        <DashboardSidebar />

        <main class="ml-5 w-[83%] max-md:ml-0 max-md:w-full">
          <div
            class="self-stretch my-auto w-full max-md:mt-10 max-md:max-w-full"
          >
            <DashboardTabs />

            <WelcomeSection />

            <TopLeadProviders />

            <StatsGrid />

            <TopLeadsComparison />

            <LeadStatusTable />
          </div>
        </main>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import DashboardHeader from "./DashboardHeader.vue";
import DashboardSidebar from "./DashboardSidebar.vue";
import DashboardTabs from "./DashboardTabs.vue";
import WelcomeSection from "./WelcomeSection.vue";
import TopLeadProviders from "./TopLeadProviders.vue";
import StatsGrid from "./StatsGrid.vue";
import TopLeadsComparison from "./TopLeadsComparison.vue";
import LeadStatusTable from "./LeadStatusTable.vue";
</script>
