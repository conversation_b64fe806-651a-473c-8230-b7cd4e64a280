"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const categories = require("./categories.cjs");
const plugins = require("./plugins.cjs");
const rules = require("./rules.cjs");
const handleOverridesScope = (overrides, configs, baseCategories) => {
  for (const overrideIndex in overrides) {
    const override = overrides[overrideIndex];
    const eslintRules = {};
    const eslintConfig = {
      name: `oxlint/from-oxlint-config-override-${overrideIndex}`
    };
    eslintConfig.files = override.files;
    const plugins$1 = plugins.readPluginsFromConfig(override);
    if (baseCategories !== void 0 && plugins$1 !== void 0) {
      categories.handleCategoriesScope(plugins$1, baseCategories, eslintRules);
    }
    const rules$1 = rules.readRulesFromConfig(override);
    if (rules$1 !== void 0) {
      rules.handleRulesScope(rules$1, eslintRules);
    }
    eslintConfig.rules = eslintRules;
    configs.push(eslintConfig);
  }
};
const readOverridesFromConfig = (config) => {
  return "overrides" in config && Array.isArray(config.overrides) ? config.overrides : void 0;
};
exports.handleOverridesScope = handleOverridesScope;
exports.readOverridesFromConfig = readOverridesFromConfig;
