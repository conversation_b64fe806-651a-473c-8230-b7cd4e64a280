// These rules are automatically generated by scripts/generate-rules.ts

import * as rules from './rules-by-category.js';

const pedanticConfig = {
  name: 'oxlint/pedantic',
  rules: rules.pedanticRules,
};

const suspiciousConfig = {
  name: 'oxlint/suspicious',
  rules: rules.suspiciousRules,
};

const styleConfig = {
  name: 'oxlint/style',
  rules: rules.styleRules,
};

const restrictionConfig = {
  name: 'oxlint/restriction',
  rules: rules.restrictionRules,
};

const correctnessConfig = {
  name: 'oxlint/correctness',
  rules: rules.correctnessRules,
};

const perfConfig = {
  name: 'oxlint/perf',
  rules: rules.perfRules,
};

const configByCategory = {
  'flat/pedantic': pedanticConfig,
  'flat/suspicious': suspiciousConfig,
  'flat/style': styleConfig,
  'flat/restriction': restrictionConfig,
  'flat/correctness': correctnessConfig,
  'flat/perf': perfConfig,
};

export default configByCategory;
