{"name": "npm-run-all2", "type": "commonjs", "version": "8.0.4", "description": "A CLI tool to run multiple npm-scripts in parallel or sequential. (Maintenance fork)", "bin": {"run-p": "bin/run-p/index.js", "run-s": "bin/run-s/index.js", "npm-run-all": "bin/npm-run-all/index.js", "npm-run-all2": "bin/npm-run-all/index.js"}, "main": "lib/index.js", "engines": {"node": "^20.5.0 || >=22.0.0", "npm": ">= 10"}, "scripts": {"clean": "rm -rf coverage jsdoc \"test-workspace/{build,test.txt}\"", "docs": "jsdoc -c jsdoc.json", "check:installed-check": "installed-check --ignore-dev", "check:eslint": "eslint", "check:knip": "knip", "precheck": "node scripts/make-slink.js", "check": "node ./bin/run-p/index.js check:*", "pretest-mocha": "node scripts/make-slink.js", "test-mocha": "c8 mocha --timeout 120000", "test": "node ./bin/run-s/index.js check test-mocha", "watch": "mocha --timeout 120000 --watch --growl", "version": "auto-changelog -p --template keepachangelog auto-changelog --breaking-pattern 'BREAKING CHANGE:' && git add CHANGELOG.md", "prepublishOnly": "git push --follow-tags && gh-release -y"}, "dependencies": {"ansi-styles": "^6.2.1", "cross-spawn": "^7.0.6", "memorystream": "^0.3.1", "picomatch": "^4.0.2", "pidtree": "^0.6.0", "read-package-json-fast": "^4.0.0", "shell-quote": "^1.7.3", "which": "^5.0.0"}, "devDependencies": {"auto-changelog": "^2.2.0", "c8": "^10.0.0", "eslint": "^9.6.0", "gh-release": "^7.0.0", "installed-check": "^9.3.0", "jsdoc": "^4.0.0", "knip": "^5.23.3", "mocha": "^11.0.1", "neostandard": "^0.12.0", "yarn": "^1.12.3"}, "repository": {"type": "git", "url": "https://github.com/bcomnes/npm-run-all2.git"}, "keywords": ["cli", "command", "commandline", "tool", "npm", "npm-scripts", "run", "sequential", "serial", "parallel", "task"], "author": "<PERSON><PERSON>", "contributors": ["<PERSON><PERSON> Comnes <<EMAIL>> (https://bret.io)"], "license": "MIT", "bugs": {"url": "https://github.com/bcomnes/npm-run-all2/issues"}, "homepage": "https://github.com/bcomnes/npm-run-all2", "c8": {"reporter": ["html", "lcovonly", "text"]}}