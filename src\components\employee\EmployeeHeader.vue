<template>
  <header
    class="flex flex-wrap gap-5 justify-between py-2.5 pr-6 pl-1.5 w-full bg-white shadow-[0px_2px_12px_rgba(0,0,0,0.05)] max-md:pr-5 max-md:max-w-full"
  >
    <!-- Logo Section -->
    <div class="flex gap-3.5 self-start">
      <div class="flex gap-1.5">
        <img
          :src="companyLogo"
          class="object-contain shrink-0 w-10 rounded-lg aspect-square"
          alt="Company logo"
        />
        <div class="shrink-0 my-auto w-px h-4 border border-solid border-slate-300"></div>
      </div>
      <img
        :src="brandLogo"
        class="object-contain shrink-0 self-start aspect-[2.16] w-[84px]"
        alt="Brand logo"
      />
    </div>

    <!-- Navigation Section -->
    <nav class="flex gap-3.5 items-center text-base leading-none text-sky-950">
      <div class="flex flex-wrap gap-3.5 items-center self-stretch max-md:max-w-full">
        <!-- Search -->
        <div class="flex gap-4 self-stretch px-4 py-2.5 text-sm leading-none rounded-xl border border-solid border-slate-300 text-slate-500">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Customer Search..."
            class="grow my-auto bg-transparent outline-none"
            @keyup.enter="handleSearch"
          />
          <button @click="handleSearch" class="flex items-center">
            <SearchIcon class="w-6 h-6" />
          </button>
        </div>

        <div class="shrink-0 self-stretch my-auto w-px h-4 border border-solid border-slate-300"></div>

        <!-- Notifications -->
        <NotificationBadge
          :count="newLeadsCount"
          label="New Leads"
          icon="leads"
        />

        <NotificationBadge
          :count="messagesCount"
          label="Messages"
          icon="messages"
        />

        <div class="shrink-0 self-stretch my-auto w-px h-4 border border-solid border-slate-300"></div>
      </div>

      <!-- User Profile -->
      <UserProfile
        :user="currentUser"
        @profile-click="handleProfileClick"
      />
    </nav>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import NotificationBadge from './NotificationBadge.vue'
import UserProfile from './UserProfile.vue'
import SearchIcon from './icons/SearchIcon.vue'

interface User {
  id: string
  name: string
  avatar?: string
}

interface Props {
  companyLogo?: string
  brandLogo?: string
  currentUser: User
  newLeadsCount?: number
  messagesCount?: number
}

interface Emits {
  search: [query: string]
  profileClick: [user: User]
}

const props = withDefaults(defineProps<Props>(), {
  companyLogo: 'https://cdn.builder.io/api/v1/image/assets/TEMP/8aa352046bde861dd9cb0b98904b26054bbb8e7f?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde',
  brandLogo: 'https://cdn.builder.io/api/v1/image/assets/TEMP/8d2019e145d5ce1ae222efb945bb787f5d6f0856?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde',
  newLeadsCount: 0,
  messagesCount: 0
})

const emit = defineEmits<Emits>()

const searchQuery = ref('')

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    emit('search', searchQuery.value.trim())
  }
}

const handleProfileClick = () => {
  emit('profileClick', props.currentUser)
}
</script>
