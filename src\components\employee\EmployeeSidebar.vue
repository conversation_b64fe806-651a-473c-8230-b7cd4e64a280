<template>
  <aside class="w-[17%] max-md:ml-0 max-md:w-full">
    <nav class="grow text-base font-bold leading-8 text-cyan-600">
      <div class="flex z-10 flex-col items-start px-3.5 pt-6 pb-16 w-full bg-white">
        <!-- Search -->
        <div class="flex gap-5 justify-between self-stretch px-4 py-2.5 text-sm leading-none rounded-xl border border-solid border-slate-300 text-slate-500">
          <input
            v-model="menuSearch"
            type="text"
            placeholder="Menu search..."
            class="my-auto bg-transparent outline-none flex-1"
          />
          <SearchIcon class="w-6 h-6" />
        </div>

        <!-- Menu Sections -->
        <SidebarSection
          v-for="section in filteredSections"
          :key="section.title"
          :title="section.title"
          :items="section.items"
          :active-item="activeMenuItem"
          @item-click="handleMenuClick"
        />
      </div>

      <div class="flex shrink-0 -mt-20 bg-white h-[524px]"></div>
    </nav>
  </aside>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import SidebarSection from './SidebarSection.vue'
import SearchIcon from './icons/SearchIcon.vue'

interface MenuItem {
  id: string
  label: string
  route?: string
  action?: string
}

interface MenuSection {
  title: string
  items: MenuItem[]
}

interface Props {
  activeMenuItem?: string
}

interface Emits {
  menuClick: [item: MenuItem]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const menuSearch = ref('')

const menuSections: MenuSection[] = [
  {
    title: 'Menu',
    items: [
      { id: 'build-call-list', label: 'Build Call List', route: '/employee/call-list' },
      { id: 'customer-query', label: 'Customer Query', route: '/employee/customers' },
      { id: 'incoming-applications', label: 'Incoming Applications', route: '/employee/applications/incoming' },
      { id: 'insight-center', label: 'Insight Center', route: '/employee/insights' },
      { id: 'instant-messenger', label: 'Instant Messenger', route: '/employee/messenger' },
      { id: 'loans', label: 'Loans', route: '/employee/loans' },
      { id: 'new-application', label: 'New Application', route: '/employee/applications/new' },
      { id: 'pending-applications', label: 'Pending Applications', route: '/employee/applications/pending' },
      { id: 'portfolio-analysis', label: 'Portfolio Analysis', route: '/employee/portfolio' },
      { id: 'queues', label: 'Queues', route: '/employee/queues' },
      { id: 'report-builder', label: 'Report Builder', route: '/employee/reports/builder' },
      { id: 'report-results', label: 'Report Results', route: '/employee/reports/results' },
      { id: 'reports', label: 'Reports', route: '/employee/reports' }
    ]
  },
  {
    title: 'Information Tables',
    items: [
      { id: 'bad-aba', label: 'Bad ABA Numbers', route: '/employee/info/bad-aba' },
      { id: 'bad-employment', label: 'Bad Employment', route: '/employee/info/bad-employment' },
      { id: 'bad-ssn', label: 'Bad SSN', route: '/employee/info/bad-ssn' },
      { id: 'bad-zip', label: 'Bad ZIP Codes', route: '/employee/info/bad-zip' },
      { id: 'do-not-business', label: 'Do Not Do Business', route: '/employee/info/do-not-business' },
      { id: 'ofac-sdn', label: 'OFAC SDN', route: '/employee/info/ofac-sdn' }
    ]
  },
  {
    title: 'ACH',
    items: [
      { id: 'ach-batch-processing', label: 'ACH Batch Processing Queue', route: '/employee/ach/batch-processing' },
      { id: 'ach-batch-renewal', label: 'ACH Batch Renewal Queue', route: '/employee/ach/batch-renewal' },
      { id: 'batch-files', label: 'Batch Files Management', route: '/employee/ach/batch-files' },
      { id: 'returned-files', label: 'Returned Files Management', route: '/employee/ach/returned-files' },
      { id: 'settled-files', label: 'Settled Files Management', route: '/employee/ach/settled-files' }
    ]
  },
  {
    title: 'Debit Card',
    items: [
      { id: 'generation-queue', label: 'Generation Queue', route: '/employee/debit/generation' },
      { id: 'reattempt-queue', label: 'Reattempt Queue', route: '/employee/debit/reattempt' },
      { id: 'status-queue', label: 'Status Queue', route: '/employee/debit/status' }
    ]
  }
]

const filteredSections = computed(() => {
  if (!menuSearch.value) return menuSections
  
  return menuSections.map(section => ({
    ...section,
    items: section.items.filter(item =>
      item.label.toLowerCase().includes(menuSearch.value.toLowerCase())
    )
  })).filter(section => section.items.length > 0)
})

const handleMenuClick = (item: MenuItem) => {
  emit('menuClick', item)
}
</script>
