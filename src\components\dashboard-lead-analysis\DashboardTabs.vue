<template>
  <div
    class="py-3 pr-6 pl-2 w-full text-white border-t-0 bg-teal-950 border-slate-300 max-md:pr-5 max-md:max-w-full"
  >
    <div class="flex flex-wrap gap-10 w-full max-md:max-w-full">
      <div class="flex flex-1 gap-6 my-auto">
        <h2 class="grow my-auto text-sm font-bold leading-none">
          My Contact Info
        </h2>

        <div
          class="flex flex-auto gap-3 text-xs font-medium leading-loose whitespace-nowrap"
        >
          <img
            src="https://cdn.builder.io/api/v1/image/assets/TEMP/680ec530bc1dd0a183574a7c2d29cd8bc1ecde55?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
            class="object-contain shrink-0 w-14 rounded-lg aspect-[2.33]"
            alt="Contact info icon"
          />
          <div class="flex gap-2.5 px-2 py-0.5 bg-cyan-600 rounded-lg">
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/ab1625e22259d44f8b369ab1a6c2fdadcd8d1d24?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
              class="object-contain shrink-0 my-auto w-3.5 aspect-square"
              alt="Email icon"
            />
            <span class="basis-auto"><EMAIL></span>
          </div>
        </div>
      </div>

      <div
        class="flex flex-1 gap-3 items-center text-sm font-medium leading-none"
      >
        <img
          src="https://cdn.builder.io/api/v1/image/assets/TEMP/7f0c85613f8956f8eb09299d7a7dc49466bd16a6?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
          class="object-contain shrink-0 self-stretch w-7 aspect-square"
          alt="Auto refresh icon"
        />
        <span class="self-stretch my-auto">Auto Refresh</span>

        <div
          class="shrink-0 self-stretch my-auto w-px h-4 border border-solid border-slate-300"
        ></div>

        <img
          src="https://cdn.builder.io/api/v1/image/assets/TEMP/9cb5f60a67e2105bb835d2405d0bf9f2c1d4071b?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
          class="object-contain shrink-0 self-stretch my-auto w-6 aspect-square"
          alt="Stores icon"
        />
        <span class="self-stretch my-auto">All Stores</span>
        <img
          src="https://cdn.builder.io/api/v1/image/assets/TEMP/d3976838fd54b99de7a50571fcdb99bdfc32b21b?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
          class="object-contain shrink-0 self-stretch my-auto w-3.5 aspect-[1.56]"
          alt="Dropdown arrow"
        />

        <div
          class="shrink-0 self-stretch my-auto w-px h-4 border border-solid border-slate-300"
        ></div>

        <img
          src="https://cdn.builder.io/api/v1/image/assets/TEMP/592f15c23f65f5213d51c21b422b6a3be01297ab?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
          class="object-contain shrink-0 self-stretch my-auto w-6 aspect-square"
          alt="Filters icon"
        />
        <span class="self-stretch my-auto">Filters (0)</span>
        <img
          src="https://cdn.builder.io/api/v1/image/assets/TEMP/d3976838fd54b99de7a50571fcdb99bdfc32b21b?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
          class="object-contain shrink-0 self-stretch my-auto w-3.5 aspect-[1.56]"
          alt="Dropdown arrow"
        />
      </div>
    </div>

    <nav class="flex mt-2 max-w-full text-xs leading-loose w-[822px]">
      <div class="flex shrink-0 h-6 bg-cyan-600 rounded-lg w-[88px]"></div>
      <div
        class="flex flex-wrap flex-auto gap-2.5 items-center self-start min-h-5"
      >
        <span class="self-stretch my-auto font-semibold text-cyan-600">
          Lead Analysis
        </span>
        <div
          class="grow shrink self-stretch my-auto w-0 h-4 border border-solid border-slate-300"
        ></div>
        <span class="self-stretch my-auto">New Loan Summary</span>
        <div
          class="grow shrink self-stretch my-auto w-0 h-4 border border-solid border-slate-300"
        ></div>
        <span class="self-stretch my-auto">CPA by Lead Provider</span>
        <div
          class="grow shrink self-stretch my-auto w-0 h-4 border border-solid border-slate-300"
        ></div>
        <span class="self-stretch my-auto">Promise to Pay Summary</span>
        <div
          class="grow shrink self-stretch my-auto w-0 h-4 border border-solid border-slate-300"
        ></div>
        <span class="self-stretch my-auto">Collection Age Bucket</span>
        <div
          class="grow shrink self-stretch my-auto w-0 h-4 border border-solid border-slate-300"
        ></div>
        <span class="self-stretch my-auto">Defaulted vs. Payments Report</span>
      </div>
    </nav>
  </div>
</template>

<script setup lang="ts">
// No props needed for this component
</script>
