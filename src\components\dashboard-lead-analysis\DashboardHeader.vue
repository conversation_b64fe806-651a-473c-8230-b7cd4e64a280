<template>
  <header
    class="flex flex-wrap gap-5 justify-between py-2.5 pr-6 pl-1.5 w-full bg-white shadow-[0px_2px_12px_rgba(0,0,0,0.05)] max-md:pr-5 max-md:max-w-full"
  >
    <div class="flex gap-3.5 self-start">
      <div class="flex gap-1.5">
        <img
          src="https://cdn.builder.io/api/v1/image/assets/TEMP/8aa352046bde861dd9cb0b98904b26054bbb8e7f?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
          class="object-contain shrink-0 w-10 rounded-lg aspect-square"
          alt="Company logo"
        />
        <div
          class="shrink-0 my-auto w-px h-4 border border-solid border-slate-300"
        ></div>
      </div>
      <img
        src="https://cdn.builder.io/api/v1/image/assets/TEMP/8d2019e145d5ce1ae222efb945bb787f5d6f0856?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
        class="object-contain shrink-0 self-start aspect-[2.16] w-[84px]"
        alt="Brand logo"
      />
    </div>

    <nav class="flex gap-3.5 items-center text-base leading-none text-sky-950">
      <div
        class="flex flex-wrap gap-3.5 items-center self-stretch max-md:max-w-full"
      >
        <div
          class="flex gap-4 self-stretch px-4 py-2.5 text-sm leading-none rounded-xl border border-solid border-slate-300 text-slate-500"
        >
          <label class="grow my-auto">Customer Search...</label>
          <img
            src="https://cdn.builder.io/api/v1/image/assets/TEMP/6184f0451374f0a49036ddf0d04fd6fc94a5260f?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
            class="object-contain shrink-0 w-6 aspect-square"
            alt="Search icon"
          />
        </div>

        <div
          class="shrink-0 self-stretch my-auto w-px h-4 border border-solid border-slate-300"
        ></div>

        <div class="flex gap-1.5 items-center self-stretch my-auto min-h-6">
          <img
            src="https://cdn.builder.io/api/v1/image/assets/TEMP/4f9cdfd1abf1b9b35ffc7bddbaf59e745636cd33?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
            class="object-contain shrink-0 self-stretch my-auto w-6 aspect-square"
            alt="Leads icon"
          />
          <span class="self-stretch my-auto">
            <strong>28</strong> New Leads
          </span>
        </div>

        <div class="flex gap-1.5 items-center self-stretch my-auto min-h-6">
          <img
            src="https://cdn.builder.io/api/v1/image/assets/TEMP/ba9086e318fbb43d0a07d6e1c4f8c8ffaf85bc7e?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
            class="object-contain shrink-0 self-stretch my-auto w-6 aspect-square"
            alt="Messages icon"
          />
          <span class="self-stretch my-auto">
            <strong>3</strong> Messages
          </span>
        </div>

        <div
          class="shrink-0 self-stretch my-auto w-px h-4 border border-solid border-slate-300"
        ></div>
      </div>

      <div class="flex gap-1.5 self-stretch my-auto font-bold">
        <img
          src="https://cdn.builder.io/api/v1/image/assets/TEMP/6e660936d2efcdbfb8b39739e13236f08ab20a85?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
          class="object-contain shrink-0 w-6 aspect-square"
          alt="User avatar"
        />
        <span class="my-auto">John Doe</span>
      </div>

      <img
        src="https://cdn.builder.io/api/v1/image/assets/TEMP/d3976838fd54b99de7a50571fcdb99bdfc32b21b?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
        class="object-contain shrink-0 self-stretch my-auto w-3.5 aspect-[1.56]"
        alt="Dropdown arrow"
      />
    </nav>
  </header>
</template>

<script setup lang="ts">
// No props needed for this component
</script>
