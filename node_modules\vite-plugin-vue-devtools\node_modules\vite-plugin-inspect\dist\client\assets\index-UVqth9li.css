/* BASICS */

.CodeMirror {
  /* Set height, width, borders, and global font properties here */
  font-family: monospace;
  height: 300px;
  color: black;
  direction: ltr;
}

/* PADDING */

.CodeMirror-lines {
  padding: 4px 0; /* Vertical padding around content */
}
.CodeMirror pre.CodeMirror-line,
.CodeMirror pre.CodeMirror-line-like {
  padding: 0 4px; /* Horizontal padding of content */
}

.CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {
  background-color: white; /* The little square between H and V scrollbars */
}

/* GUTTER */

.CodeMirror-gutters {
  border-right: 1px solid #ddd;
  background-color: #f7f7f7;
  white-space: nowrap;
}
.CodeMirror-linenumbers {}
.CodeMirror-linenumber {
  padding: 0 3px 0 5px;
  min-width: 20px;
  text-align: right;
  color: #999;
  white-space: nowrap;
}

.CodeMirror-guttermarker { color: black; }
.CodeMirror-guttermarker-subtle { color: #999; }

/* CURSOR */

.CodeMirror-cursor {
  border-left: 1px solid black;
  border-right: none;
  width: 0;
}
/* Shown when moving in bi-directional text */
.CodeMirror div.CodeMirror-secondarycursor {
  border-left: 1px solid silver;
}
.cm-fat-cursor .CodeMirror-cursor {
  width: auto;
  border: 0 !important;
  background: #7e7;
}
.cm-fat-cursor div.CodeMirror-cursors {
  z-index: 1;
}
.cm-fat-cursor .CodeMirror-line::selection,
.cm-fat-cursor .CodeMirror-line > span::selection, 
.cm-fat-cursor .CodeMirror-line > span > span::selection { background: transparent; }
.cm-fat-cursor .CodeMirror-line::-moz-selection,
.cm-fat-cursor .CodeMirror-line > span::-moz-selection,
.cm-fat-cursor .CodeMirror-line > span > span::-moz-selection { background: transparent; }
.cm-fat-cursor { caret-color: transparent; }
@-moz-keyframes blink {
  0% {}
  50% { background-color: transparent; }
  100% {}
}
@-webkit-keyframes blink {
  0% {}
  50% { background-color: transparent; }
  100% {}
}
@keyframes blink {
  0% {}
  50% { background-color: transparent; }
  100% {}
}

/* Can style cursor different in overwrite (non-insert) mode */
.CodeMirror-overwrite .CodeMirror-cursor {}

.cm-tab { display: inline-block; text-decoration: inherit; }

.CodeMirror-rulers {
  position: absolute;
  left: 0; right: 0; top: -50px; bottom: 0;
  overflow: hidden;
}
.CodeMirror-ruler {
  border-left: 1px solid #ccc;
  top: 0; bottom: 0;
  position: absolute;
}

/* DEFAULT THEME */

.cm-s-default .cm-header {color: blue;}
.cm-s-default .cm-quote {color: #090;}
.cm-negative {color: #d44;}
.cm-positive {color: #292;}
.cm-header, .cm-strong {font-weight: bold;}
.cm-em {font-style: italic;}
.cm-link {text-decoration: underline;}
.cm-strikethrough {text-decoration: line-through;}

.cm-s-default .cm-keyword {color: #708;}
.cm-s-default .cm-atom {color: #219;}
.cm-s-default .cm-number {color: #164;}
.cm-s-default .cm-def {color: #00f;}
.cm-s-default .cm-variable,
.cm-s-default .cm-punctuation,
.cm-s-default .cm-property,
.cm-s-default .cm-operator {}
.cm-s-default .cm-variable-2 {color: #05a;}
.cm-s-default .cm-variable-3, .cm-s-default .cm-type {color: #085;}
.cm-s-default .cm-comment {color: #a50;}
.cm-s-default .cm-string {color: #a11;}
.cm-s-default .cm-string-2 {color: #f50;}
.cm-s-default .cm-meta {color: #555;}
.cm-s-default .cm-qualifier {color: #555;}
.cm-s-default .cm-builtin {color: #30a;}
.cm-s-default .cm-bracket {color: #997;}
.cm-s-default .cm-tag {color: #170;}
.cm-s-default .cm-attribute {color: #00c;}
.cm-s-default .cm-hr {color: #999;}
.cm-s-default .cm-link {color: #00c;}

.cm-s-default .cm-error {color: #f00;}
.cm-invalidchar {color: #f00;}

.CodeMirror-composing { border-bottom: 2px solid; }

/* Default styles for common addons */

div.CodeMirror span.CodeMirror-matchingbracket {color: #0b0;}
div.CodeMirror span.CodeMirror-nonmatchingbracket {color: #a22;}
.CodeMirror-matchingtag { background: rgba(255, 150, 0, .3); }
.CodeMirror-activeline-background {background: #e8f2ff;}

/* STOP */

/* The rest of this file contains styles related to the mechanics of
   the editor. You probably shouldn't touch them. */

.CodeMirror {
  position: relative;
  overflow: hidden;
  background: white;
}

.CodeMirror-scroll {
  overflow: scroll !important; /* Things will break if this is overridden */
  /* 50px is the magic margin used to hide the element's real scrollbars */
  /* See overflow: hidden in .CodeMirror */
  margin-bottom: -50px; margin-right: -50px;
  padding-bottom: 50px;
  height: 100%;
  outline: none; /* Prevent dragging from highlighting the element */
  position: relative;
  z-index: 0;
}
.CodeMirror-sizer {
  position: relative;
  border-right: 50px solid transparent;
}

/* The fake, visible scrollbars. Used to force redraw during scrolling
   before actual scrolling happens, thus preventing shaking and
   flickering artifacts. */
.CodeMirror-vscrollbar, .CodeMirror-hscrollbar, .CodeMirror-scrollbar-filler, .CodeMirror-gutter-filler {
  position: absolute;
  z-index: 6;
  display: none;
  outline: none;
}
.CodeMirror-vscrollbar {
  right: 0; top: 0;
  overflow-x: hidden;
  overflow-y: scroll;
}
.CodeMirror-hscrollbar {
  bottom: 0; left: 0;
  overflow-y: hidden;
  overflow-x: scroll;
}
.CodeMirror-scrollbar-filler {
  right: 0; bottom: 0;
}
.CodeMirror-gutter-filler {
  left: 0; bottom: 0;
}

.CodeMirror-gutters {
  position: absolute; left: 0; top: 0;
  min-height: 100%;
  z-index: 3;
}
.CodeMirror-gutter {
  white-space: normal;
  height: 100%;
  display: inline-block;
  vertical-align: top;
  margin-bottom: -50px;
}
.CodeMirror-gutter-wrapper {
  position: absolute;
  z-index: 4;
  background: none !important;
  border: none !important;
}
.CodeMirror-gutter-background {
  position: absolute;
  top: 0; bottom: 0;
  z-index: 4;
}
.CodeMirror-gutter-elt {
  position: absolute;
  cursor: default;
  z-index: 4;
}
.CodeMirror-gutter-wrapper ::selection { background-color: transparent }
.CodeMirror-gutter-wrapper ::-moz-selection { background-color: transparent }

.CodeMirror-lines {
  cursor: text;
  min-height: 1px; /* prevents collapsing before first draw */
}
.CodeMirror pre.CodeMirror-line,
.CodeMirror pre.CodeMirror-line-like {
  /* Reset some styles that the rest of the page might have set */
  -moz-border-radius: 0; -webkit-border-radius: 0; border-radius: 0;
  border-width: 0;
  background: transparent;
  font-family: inherit;
  font-size: inherit;
  margin: 0;
  white-space: pre;
  word-wrap: normal;
  line-height: inherit;
  color: inherit;
  z-index: 2;
  position: relative;
  overflow: visible;
  -webkit-tap-highlight-color: transparent;
  -webkit-font-variant-ligatures: contextual;
  font-variant-ligatures: contextual;
}
.CodeMirror-wrap pre.CodeMirror-line,
.CodeMirror-wrap pre.CodeMirror-line-like {
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: normal;
}

.CodeMirror-linebackground {
  position: absolute;
  left: 0; right: 0; top: 0; bottom: 0;
  z-index: 0;
}

.CodeMirror-linewidget {
  position: relative;
  z-index: 2;
  padding: 0.1px; /* Force widget margins to stay inside of the container */
}

.CodeMirror-widget {}

.CodeMirror-rtl pre { direction: rtl; }

.CodeMirror-code {
  outline: none;
}

/* Force content-box sizing for the elements where we expect it */
.CodeMirror-scroll,
.CodeMirror-sizer,
.CodeMirror-gutter,
.CodeMirror-gutters,
.CodeMirror-linenumber {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}

.CodeMirror-measure {
  position: absolute;
  width: 100%;
  height: 0;
  overflow: hidden;
  visibility: hidden;
}

.CodeMirror-cursor {
  position: absolute;
  pointer-events: none;
}
.CodeMirror-measure pre { position: static; }

div.CodeMirror-cursors {
  visibility: hidden;
  position: relative;
  z-index: 3;
}
div.CodeMirror-dragcursors {
  visibility: visible;
}

.CodeMirror-focused div.CodeMirror-cursors {
  visibility: visible;
}

.CodeMirror-selected { background: #d9d9d9; }
.CodeMirror-focused .CodeMirror-selected { background: #d7d4f0; }
.CodeMirror-crosshair { cursor: crosshair; }
.CodeMirror-line::selection, .CodeMirror-line > span::selection, .CodeMirror-line > span > span::selection { background: #d7d4f0; }
.CodeMirror-line::-moz-selection, .CodeMirror-line > span::-moz-selection, .CodeMirror-line > span > span::-moz-selection { background: #d7d4f0; }

.cm-searching {
  background-color: #ffa;
  background-color: rgba(255, 255, 0, .4);
}

/* Used to force a border model for a node */
.cm-force-border { padding-right: .1px; }

@media print {
  /* Hide the cursor when printing */
  .CodeMirror div.CodeMirror-cursors {
    visibility: hidden;
  }
}

/* See issue #2901 */
.cm-tab-wrap-hack:after { content: ''; }

/* Help users use markselection to safely style text background */
span.CodeMirror-selectedtext { background: none; }

.diff-added {
  background-color:rgb(74 222 128 / 0.15) /* #4ade80 */;
}
.diff-removed {
  background-color:rgb(248 113 113 / 0.15) /* #f87171 */;
}
.diff-added-inline {
  background-color:rgb(74 222 128 / 0.3) /* #4ade80 */;
}
.diff-removed-inline {
  background-color:rgb(248 113 113 / 0.3) /* #f87171 */;
}
/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
2. [UnoCSS]: allow to override the default border color with css var `--un-default-border-color`
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: var(--un-default-border-color, #e5e7eb); /* 2 */
}

::before,
::after {
  --un-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS.
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/*
Make elements with the HTML hidden attribute stay hidden by default.
*/

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
.splitpanes{display:flex;width:100%;height:100%}.splitpanes--vertical{flex-direction:row}.splitpanes--horizontal{flex-direction:column}.splitpanes--dragging *{user-select:none}.splitpanes__pane{width:100%;height:100%;overflow:hidden}.splitpanes--vertical .splitpanes__pane{transition:width .2s ease-out}.splitpanes--horizontal .splitpanes__pane{transition:height .2s ease-out}.splitpanes--dragging .splitpanes__pane{transition:none}.splitpanes__splitter{touch-action:none}.splitpanes--vertical>.splitpanes__splitter{min-width:1px;cursor:col-resize}.splitpanes--horizontal>.splitpanes__splitter{min-height:1px;cursor:row-resize}.splitpanes.default-theme .splitpanes__pane{background-color:#f2f2f2}.splitpanes.default-theme .splitpanes__splitter{background-color:#fff;box-sizing:border-box;position:relative;flex-shrink:0}.splitpanes.default-theme .splitpanes__splitter:before,.splitpanes.default-theme .splitpanes__splitter:after{content:"";position:absolute;top:50%;left:50%;background-color:#00000026;transition:background-color .3s}.splitpanes.default-theme .splitpanes__splitter:hover:before,.splitpanes.default-theme .splitpanes__splitter:hover:after{background-color:#00000040}.splitpanes.default-theme .splitpanes__splitter:first-child{cursor:auto}.default-theme.splitpanes .splitpanes .splitpanes__splitter{z-index:1}.default-theme.splitpanes--vertical>.splitpanes__splitter,.default-theme .splitpanes--vertical>.splitpanes__splitter{width:7px;border-left:1px solid #eee;margin-left:-1px}.default-theme.splitpanes--vertical>.splitpanes__splitter:before,.default-theme.splitpanes--vertical>.splitpanes__splitter:after,.default-theme .splitpanes--vertical>.splitpanes__splitter:before,.default-theme .splitpanes--vertical>.splitpanes__splitter:after{transform:translateY(-50%);width:1px;height:30px}.default-theme.splitpanes--vertical>.splitpanes__splitter:before,.default-theme .splitpanes--vertical>.splitpanes__splitter:before{margin-left:-2px}.default-theme.splitpanes--vertical>.splitpanes__splitter:after,.default-theme .splitpanes--vertical>.splitpanes__splitter:after{margin-left:1px}.default-theme.splitpanes--horizontal>.splitpanes__splitter,.default-theme .splitpanes--horizontal>.splitpanes__splitter{height:7px;border-top:1px solid #eee;margin-top:-1px}.default-theme.splitpanes--horizontal>.splitpanes__splitter:before,.default-theme.splitpanes--horizontal>.splitpanes__splitter:after,.default-theme .splitpanes--horizontal>.splitpanes__splitter:before,.default-theme .splitpanes--horizontal>.splitpanes__splitter:after{transform:translate(-50%);width:30px;height:1px}.default-theme.splitpanes--horizontal>.splitpanes__splitter:before,.default-theme .splitpanes--horizontal>.splitpanes__splitter:before{margin-top:-2px}.default-theme.splitpanes--horizontal>.splitpanes__splitter:after,.default-theme .splitpanes--horizontal>.splitpanes__splitter:after{margin-top:1px}.resize-observer[data-v-b329ee4c]{position:absolute;top:0;left:0;z-index:-1;width:100%;height:100%;border:none;background-color:transparent;pointer-events:none;display:block;overflow:hidden;opacity:0}.resize-observer[data-v-b329ee4c] object{display:block;position:absolute;top:0;left:0;height:100%;width:100%;overflow:hidden;pointer-events:none;z-index:-1}.v-popper__popper{z-index:10000;top:0;left:0;outline:none}.v-popper__popper.v-popper__popper--hidden{visibility:hidden;opacity:0;transition:opacity .15s,visibility .15s;pointer-events:none}.v-popper__popper.v-popper__popper--shown{visibility:visible;opacity:1;transition:opacity .15s}.v-popper__popper.v-popper__popper--skip-transition,.v-popper__popper.v-popper__popper--skip-transition>.v-popper__wrapper{transition:none!important}.v-popper__backdrop{position:absolute;top:0;left:0;width:100%;height:100%;display:none}.v-popper__inner{position:relative;box-sizing:border-box;overflow-y:auto}.v-popper__inner>div{position:relative;z-index:1;max-width:inherit;max-height:inherit}.v-popper__arrow-container{position:absolute;width:10px;height:10px}.v-popper__popper--arrow-overflow .v-popper__arrow-container,.v-popper__popper--no-positioning .v-popper__arrow-container{display:none}.v-popper__arrow-inner,.v-popper__arrow-outer{border-style:solid;position:absolute;top:0;left:0;width:0;height:0}.v-popper__arrow-inner{visibility:hidden;border-width:7px}.v-popper__arrow-outer{border-width:6px}.v-popper__popper[data-popper-placement^=top] .v-popper__arrow-inner,.v-popper__popper[data-popper-placement^=bottom] .v-popper__arrow-inner{left:-2px}.v-popper__popper[data-popper-placement^=top] .v-popper__arrow-outer,.v-popper__popper[data-popper-placement^=bottom] .v-popper__arrow-outer{left:-1px}.v-popper__popper[data-popper-placement^=top] .v-popper__arrow-inner,.v-popper__popper[data-popper-placement^=top] .v-popper__arrow-outer{border-bottom-width:0;border-left-color:transparent!important;border-right-color:transparent!important;border-bottom-color:transparent!important}.v-popper__popper[data-popper-placement^=top] .v-popper__arrow-inner{top:-2px}.v-popper__popper[data-popper-placement^=bottom] .v-popper__arrow-container{top:0}.v-popper__popper[data-popper-placement^=bottom] .v-popper__arrow-inner,.v-popper__popper[data-popper-placement^=bottom] .v-popper__arrow-outer{border-top-width:0;border-left-color:transparent!important;border-right-color:transparent!important;border-top-color:transparent!important}.v-popper__popper[data-popper-placement^=bottom] .v-popper__arrow-inner{top:-4px}.v-popper__popper[data-popper-placement^=bottom] .v-popper__arrow-outer{top:-6px}.v-popper__popper[data-popper-placement^=left] .v-popper__arrow-inner,.v-popper__popper[data-popper-placement^=right] .v-popper__arrow-inner{top:-2px}.v-popper__popper[data-popper-placement^=left] .v-popper__arrow-outer,.v-popper__popper[data-popper-placement^=right] .v-popper__arrow-outer{top:-1px}.v-popper__popper[data-popper-placement^=right] .v-popper__arrow-inner,.v-popper__popper[data-popper-placement^=right] .v-popper__arrow-outer{border-left-width:0;border-left-color:transparent!important;border-top-color:transparent!important;border-bottom-color:transparent!important}.v-popper__popper[data-popper-placement^=right] .v-popper__arrow-inner{left:-4px}.v-popper__popper[data-popper-placement^=right] .v-popper__arrow-outer{left:-6px}.v-popper__popper[data-popper-placement^=left] .v-popper__arrow-container{right:-10px}.v-popper__popper[data-popper-placement^=left] .v-popper__arrow-inner,.v-popper__popper[data-popper-placement^=left] .v-popper__arrow-outer{border-right-width:0;border-top-color:transparent!important;border-right-color:transparent!important;border-bottom-color:transparent!important}.v-popper__popper[data-popper-placement^=left] .v-popper__arrow-inner{left:-2px}.v-popper--theme-tooltip .v-popper__inner{background:rgba(0,0,0,.8);color:#fff;border-radius:6px;padding:7px 12px 6px}.v-popper--theme-tooltip .v-popper__arrow-outer{border-color:#000c}.v-popper--theme-dropdown .v-popper__inner{background:#fff;color:#000;border-radius:6px;border:1px solid #ddd;box-shadow:0 6px 30px #0000001a}.v-popper--theme-dropdown .v-popper__arrow-inner{visibility:visible;border-color:#fff}.v-popper--theme-dropdown .v-popper__arrow-outer{border-color:#ddd}html,
body,
#app {
  height: 100%;
  margin: 0;
  padding: 0;
}html.dark {
  background: #121212;
  color-scheme: dark;
}.CodeMirror {
  height: max-content !important;
  font-family: var(--cm-font-family) !important;
  font-size: 13px !important;
}.CodeMirror-scroll {
  overflow-y: hidden !important;
  overflow-x: auto !important;
}/* Splitpanes */.splitpanes__pane {
  transition: none !important;
}.splitpanes__splitter {
  position: relative;
}.splitpanes__splitter:before {
  position: absolute;
  left: 0;
  top: 0;
  transition: .2s ease;
  content: '';
  transition: opacity 0.4s;
  z-index: 1;
}.splitpanes__splitter:hover:before {
  background: #8881;
  opacity: 1;
}.splitpanes--vertical>.splitpanes__splitter {
  width: 0 !important;
}.splitpanes--horizontal>.splitpanes__splitter {
  height: 0 !important;
}.splitpanes--vertical>.splitpanes__splitter:before {
  left: -5px;
  right: -4px;
  height: 100%;
}.splitpanes--horizontal>.splitpanes__splitter:before {
  top: -5px;
  bottom: -4px;
  width: 100%;
}/* Overrides Floating Vue */.v-popper--theme-dropdown .v-popper__inner,
.v-popper--theme-tooltip .v-popper__inner {
  ;border-width:1px;--un-border-opacity:1;border-color:rgb(156 163 175 / var(--un-border-opacity));--un-border-opacity:0.3;border-radius:0.25rem;--un-bg-opacity:1;background-color:rgb(255 255 255 / var(--un-bg-opacity)) /* #fff */;--un-text-opacity:1;color:rgb(18 18 18 / var(--un-text-opacity)) /* #121212 */;font-family:"DM Sans",ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--un-shadow:var(--un-shadow-inset) 0 1px 3px 0 var(--un-shadow-color, rgb(0 0 0 / 0.1)),var(--un-shadow-inset) 0 1px 2px -1px var(--un-shadow-color, rgb(0 0 0 / 0.1));box-shadow:var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);
  box-shadow: 0 6px 30px #0000001a;
}.dark .v-popper--theme-dropdown .v-popper__inner,.dark .v-popper--theme-tooltip .v-popper__inner{--un-bg-opacity:1;background-color:rgb(18 18 18 / var(--un-bg-opacity)) /* #121212 */;--un-text-opacity:1;color:rgb(255 255 255 / var(--un-text-opacity)) /* #fff */;--un-shadow:var(--un-shadow-inset) 0 25px 50px -12px var(--un-shadow-color, rgb(0 0 0 / 0.25));box-shadow:var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);}.v-popper--theme-tooltip .v-popper__arrow-inner,
.v-popper--theme-dropdown .v-popper__arrow-inner {
  visibility: visible;
  ;--un-border-opacity:1;border-color:rgb(255 255 255 / var(--un-border-opacity));
}.dark .v-popper--theme-tooltip .v-popper__arrow-inner,.dark .v-popper--theme-dropdown .v-popper__arrow-inner{--un-border-opacity:1;border-color:rgb(23 23 23 / var(--un-border-opacity));}.v-popper--theme-tooltip .v-popper__arrow-outer,
.v-popper--theme-dropdown .v-popper__arrow-outer {
  --un-border-opacity:1;border-color:rgb(156 163 175 / var(--un-border-opacity));--un-border-opacity:0.3;
}.v-popper--theme-tooltip.v-popper--shown,
.v-popper--theme-tooltip.v-popper--shown * {
  transition: none !important;
}
:root {
  --cm-scheme: light;

  /* Colors */
  --cm-foreground: #6e6e6e;
  --cm-background: #f4f4f4;

  /* Tokens */
  --cm-comment: #a8a8a8;
  --cm-string: #555555;
  --cm-literal: #333333;
  --cm-keyword: #000000;
  --cm-function: #4f4f4f;
  --cm-deleted: #333333;
  --cm-class: #333333;
  --cm-builtin: #757575;
  --cm-property: #333333;
  --cm-namespace: #4f4f4f;
  --cm-punctuation: #ababab;
  --cm-decorator: var(--cm-class);
  --cm-operator: var(--cm-punctuation);
  --cm-number: var(--cm-literal);
  --cm-boolean: var(--cm-literal);
  --cm-variable: var(--cm-literal);
  --cm-constant: var(--cm-literal);
  --cm-symbol: var(--cm-literal);
  --cm-interpolation: var(--cm-literal);
  --cm-selector: var(--cm-keyword);
  --cm-keyword-control: var(--cm-keyword);
  --cm-regex: var(--cm-string);
  --cm-json-property: var(--cm-property);
  --cm-inline-background: var(--cm-background);

  /* Token Styles */
  --cm-comment-style: italic;
  --cm-url-decoration: underline;

  /* Extension */
  --cm-line-number: #a5a5a5;
  --cm-line-number-gutter: #333333;
  --cm-line-highlight-background: #eeeeee;
  --cm-selection-background: #aaaaaa;
  --cm-marker-color: var(--cm-foreground);
  --cm-marker-opacity: 0.4;
  --cm-marker-font-size: 0.8em;

  /* Font */
  --cm-font-size: 1em;
  --cm-line-height: 1.5em;
  --cm-font-family: monospace;
  --cm-inline-font-size: var(--cm-font-size);
  --cm-block-font-size: var(--cm-font-size);
  
  /* Sizing */
  --cm-tab-size: 2;
  
  --cm-block-padding-x: 1em;
  --cm-block-padding-y: 1em;
  --cm-block-margin-x: 0;
  --cm-block-margin-y: 0.5em;
  --cm-block-radius: 0.3em;
  --cm-inline-padding-x: 0.3em;
  --cm-inline-padding-y: 0.1em;
  --cm-inline-radius: 0.3em;
}

.cm-s-vars.CodeMirror {
  background-color: var(--cm-background);
  color: var(--cm-foreground);
}

.cm-s-vars .CodeMirror-gutters {
  background: var(--cm-line-number-gutter);
  color: var(--cm-line-number);
  border: none;
}

.cm-s-vars .CodeMirror-guttermarker,
.cm-s-vars .CodeMirror-guttermarker-subtle,
.cm-s-vars .CodeMirror-linenumber {
  color: var(--cm-line-number);
}

.cm-s-vars div.CodeMirror-selected {
  background: var(--cm-selection-background);
}

.cm-s-vars.CodeMirror-focused div.CodeMirror-selected {
  background: var(--cm-selection-background);
}

.cm-s-vars .CodeMirror-line::selection,
.cm-s-vars .CodeMirror-line>span::selection,
.cm-s-vars .CodeMirror-line>span>span::selection {
  background: var(--cm-selection-background);
}

.cm-s-vars .CodeMirror-line::-moz-selection,
.cm-s-vars .CodeMirror-line>span::-moz-selection,
.cm-s-vars .CodeMirror-line>span>span::-moz-selection {
  background: var(--cm-selection-background);
}

.cm-s-vars .CodeMirror-activeline-background {
  background: var(--cm-line-highlight-background);
}

.cm-s-vars .cm-keyword {
  color: var(--cm-keyword);
}

.cm-s-vars .cm-variable,
.cm-s-vars .cm-variable-2,
.cm-s-vars .cm-variable-3,
.cm-s-vars .cm-type {
  color: var(--cm-variable);
}

.cm-s-vars .cm-builtin {
  color: var(--cm-builtin);
}

.cm-s-vars .cm-atom {
  color: var(--cm-literal);
}

.cm-s-vars .cm-number {
  color: var(--cm-number);
}

.cm-s-vars .cm-def {
  color: var(--cm-decorator);
}

.cm-s-vars .cm-string,
.cm-s-vars .cm-string-2 {
  color: var(--cm-string);
}

.cm-s-vars .cm-comment {
  color: var(--cm-comment);
}

.cm-s-vars .cm-tag {
  color: var(--cm-builtin);
}

.cm-s-vars .cm-meta {
  color: var(--cm-namespace);
}

.cm-s-vars .cm-attribute {
  color: var(--cm-property);
}

.cm-s-vars .cm-property {
  color: var(--cm-property);
}

.cm-s-vars .cm-qualifier {
  color: var(--cm-keyword);
}

.cm-s-vars .cm-error {
  color: var(--prism-deleted);
}

.cm-s-vars .cm-operator,
.cm-s-vars .cm-bracket {
  color: var(--cm-punctuation);
}

.cm-s-vars .CodeMirror-matchingbracket {
  text-decoration: underline;
}

.cm-s-vars .CodeMirror-cursor {
  border-left: 1px solid currentColor;
}

:root {
  --cm-font-family: 'Input Mono', 'FiraCode', monospace;
}

html:not(.dark) {
  --cm-foreground: #8e8f8b;
  --cm-background: #ffffff;
  --cm-comment: #a0ada0;
  --cm-string: #b56959;
  --cm-literal: #2f8a89;
  --cm-number: #296aa3;
  --cm-keyword: #1c6b48;
  --cm-function: #6c7834;
  --cm-boolean: #1c6b48;
  --cm-constant: #a65e2b;
  --cm-deleted: #a14f55;
  --cm-variable: #ad944c;
  --cm-class: #2993a3;
  --cm-builtin: #ab5959;
  --cm-property: #b58451;
  --cm-namespace: #b05a78;
  --cm-punctuation: #8e8f8b;
  --cm-decorator: #bd8f8f;
  --cm-regex: #ab5e3f;
  --cm-json-property: #698c96;
  --cm-selection-background: #44444410;
  --cm-line-number-gutter: #fafafa;
}

html.dark {
  --cm-foreground: #858585;
  --cm-background: #111111;
  --cm-comment: #758575;
  --cm-string: #d48372;
  --cm-literal: #429988;
  --cm-keyword: #4d9375;
  --cm-boolean: #1c6b48;
  --cm-number: #6394bf;
  --cm-variable: #c2b36e;
  --cm-function: #a1b567;
  --cm-deleted: #a14f55;
  --cm-class: #54b1bf;
  --cm-builtin: #e0a569;
  --cm-property: #dd8e6e;
  --cm-namespace: #db889a;
  --cm-punctuation: #858585;
  --cm-decorator: #bd8f8f;
  --cm-regex: #ab5e3f;
  --cm-json-property: #6b8b9e;
  --cm-line-number: #888888;
  --cm-line-number-gutter: #eeeeee;
  --cm-line-highlight-background: #444444;
  --cm-selection-background: #44444450;
  --cm-line-number-gutter: #1a1a1a;
}
 *,::before,::after{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 rgb(0 0 0 / 0);--un-ring-shadow:0 0 rgb(0 0 0 / 0);--un-shadow-inset: ;--un-shadow:0 0 rgb(0 0 0 / 0);--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgb(147 197 253 / 0.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: ;}::backdrop{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 rgb(0 0 0 / 0);--un-ring-shadow:0 0 rgb(0 0 0 / 0);--un-shadow-inset: ;--un-shadow:0 0 rgb(0 0 0 / 0);--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgb(147 197 253 / 0.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: ;}/* latin-ext */@font-face {  font-family: 'DM Mono';  font-style: normal;  font-weight: 400;  font-display: swap;  src: url(/assets/fonts/dmmono-989a58ac.woff2) format('woff2');  unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;}/* latin */@font-face {  font-family: 'DM Mono';  font-style: normal;  font-weight: 400;  font-display: swap;  src: url(/assets/fonts/dmmono-c6e6cdda.woff2) format('woff2');  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;}/* latin-ext */@font-face {  font-family: 'DM Sans';  font-style: normal;  font-weight: 400;  font-display: swap;  src: url(/assets/fonts/dmsans-8b9140d7.woff2) format('woff2');  unicode-range: U+0100-02AF, U+0304, U+0308, U+0329, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;}/* latin */@font-face {  font-family: 'DM Sans';  font-style: normal;  font-weight: 400;  font-display: swap;  src: url(/assets/fonts/dmsans-32eb64a7.woff2) format('woff2');  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;}.dark .dark\:i-carbon-moon,.dark [dark\:i-carbon-moon=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M13.503 5.414a15.076 15.076 0 0 0 11.593 18.194a11.1 11.1 0 0 1-7.975 3.39c-.138 0-.278.005-.418 0a11.094 11.094 0 0 1-3.2-21.584M14.98 3a1 1 0 0 0-.175.016a13.096 13.096 0 0 0 1.825 25.981c.164.006.328 0 .49 0a13.07 13.07 0 0 0 10.703-5.555a1.01 1.01 0 0 0-.783-1.565A13.08 13.08 0 0 1 15.89 4.38A1.015 1.015 0 0 0 14.98 3'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-arrow-down,[i-carbon-arrow-down=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M24.59 16.59L17 24.17V4h-2v20.17l-7.59-7.58L6 18l10 10l10-10z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-arrow-left,[i-carbon-arrow-left=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='m14 26l1.41-1.41L7.83 17H28v-2H7.83l7.58-7.59L14 6L4 16z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-arrow-right,[i-carbon-arrow-right=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='m18 6l-1.43 1.393L24.15 15H4v2h20.15l-7.58 7.573L18 26l10-10z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-arrow-up,[i-carbon-arrow-up=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M16 4L6 14l1.41 1.41L15 7.83V28h2V7.83l7.59 7.58L26 14z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-chevron-left,[i-carbon-chevron-left=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M10 16L20 6l1.4 1.4l-8.6 8.6l8.6 8.6L20 26z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-chevron-sort,[i-carbon-chevron-sort=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='m16 28l-7-7l1.41-1.41L16 25.17l5.59-5.58L23 21zm0-24l7 7l-1.41 1.41L16 6.83l-5.59 5.58L9 11z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-choropleth-map,[i-carbon-choropleth-map=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='m29.243 4.03l-8-2a1 1 0 0 0-.615.042l-9.7 3.88L3.243 4.03A1 1 0 0 0 2 5v22a1 1 0 0 0 .757.97l8 2A1 1 0 0 0 11 30a1 1 0 0 0 .372-.072l9.7-3.88l7.686 1.922A1 1 0 0 0 30 27V5a1 1 0 0 0-.757-.97M28 11h-6V4.28l6 1.5Zm-18 8H4v-6h6Zm2-8V7.677l8-3.2V11Zm8 2v6h-8v-6Zm-8 8h8v3.323l-8 3.2Zm10-8h6v6h-6ZM10 7.78V11H4V6.28ZM4 21h6v6.72l-6-1.5Zm18 3.219V21h6v4.72Z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-cloud-services,[i-carbon-cloud-services=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M25.822 10.124a10 10 0 0 0-19.644 0A7.496 7.496 0 0 0 7.5 25H8v-2h-.5a5.496 5.496 0 0 1-.377-10.98l.836-.057l.09-.833a7.998 7.998 0 0 1 15.902 0l.09.833l.836.058A5.496 5.496 0 0 1 24.5 23H24v2h.5a7.496 7.496 0 0 0 1.322-14.876'/%3E%3Cpath fill='currentColor' d='M23 22v-2h-2.101a5 5 0 0 0-.732-1.753l1.49-1.49l-1.414-1.414l-1.49 1.49A5 5 0 0 0 17 16.101V14h-2v2.101a5 5 0 0 0-1.753.732l-1.49-1.49l-1.414 1.414l1.49 1.49A5 5 0 0 0 11.101 20H9v2h2.101a5 5 0 0 0 .732 1.753l-1.49 1.49l1.414 1.414l1.49-1.49a5 5 0 0 0 1.753.732V28h2v-2.101a5 5 0 0 0 1.753-.732l1.49 1.49l1.414-1.414l-1.49-1.49A5 5 0 0 0 20.899 22Zm-7 2a3 3 0 1 1 3-3a3.003 3.003 0 0 1-3 3'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-compare,[i-carbon-compare=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M28 6H18V4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v20a2 2 0 0 0 2 2h10v2a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2M4 15h6.17l-2.58 2.59L9 19l5-5l-5-5l-1.41 1.41L10.17 13H4V4h12v20H4Zm12 13v-2a2 2 0 0 0 2-2V8h10v9h-6.17l2.58-2.59L23 13l-5 5l5 5l1.41-1.41L21.83 19H28v9Z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-ibm-watson-discovery,[i-carbon-ibm-watson-discovery=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M6 17h8v2H6z'/%3E%3Ccircle cx='3' cy='18' r='1' fill='currentColor'/%3E%3Ccircle cx='13' cy='14' r='1' fill='currentColor'/%3E%3Cpath fill='currentColor' d='M2 13h8v2H2zm4-4h8v2H6z'/%3E%3Ccircle cx='3' cy='10' r='1' fill='currentColor'/%3E%3Cpath fill='currentColor' d='m30 28.6l-7.4-7.4c1.5-2 2.4-4.5 2.4-7.2c0-6.6-5.4-12-12-12c-3.3 0-6.4 1.3-8.7 3.8l1.5 1.4C7.6 5.1 10.2 4 13 4c5.5 0 10 4.5 10 10s-4.5 10-10 10c-3 0-5.8-1.3-7.7-3.6l-1.5 1.3C6 24.4 9.4 26 13 26c3.2 0 6.1-1.3 8.3-3.3l7.3 7.3z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-list,[i-carbon-list=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M10 6h18v2H10zm0 18h18v2H10zm0-9h18v2H10zm-6 0h2v2H4zm0-9h2v2H4zm0 18h2v2H4z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-list-boxes,[i-carbon-list-boxes=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M16 8h14v2H16zm0 14h14v2H16zm-6-8H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2M4 6v6h6.001L10 6zm6 22H4a2 2 0 0 1-2-2v-6a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v6a2 2 0 0 1-2 2m-6-8v6h6.001L10 20z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-logo-github,[i-carbon-logo-github=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' fill-rule='evenodd' d='M16 2a14 14 0 0 0-4.43 27.28c.7.13 1-.3 1-.67v-2.38c-3.89.84-4.71-1.88-4.71-1.88a3.7 3.7 0 0 0-1.62-2.05c-1.27-.86.1-.85.1-.85a2.94 2.94 0 0 1 2.14 1.45a3 3 0 0 0 4.08 1.16a2.93 2.93 0 0 1 .88-1.87c-3.1-.36-6.37-1.56-6.37-6.92a5.4 5.4 0 0 1 1.44-3.76a5 5 0 0 1 .14-3.7s1.17-.38 3.85 1.43a13.3 13.3 0 0 1 7 0c2.67-1.81 3.84-1.43 3.84-1.43a5 5 0 0 1 .14 3.7a5.4 5.4 0 0 1 1.44 3.76c0 5.38-3.27 6.56-6.39 6.91a3.33 3.33 0 0 1 .95 2.59v3.84c0 .46.25.81 1 .67A14 14 0 0 0 16 2'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-menu,[i-carbon-menu=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M4 6h24v2H4zm0 18h24v2H4zm0-12h24v2H4zm0 6h24v2H4z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-meter,[i-carbon-meter=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M26 16a9.9 9.9 0 0 0-1.14-4.618l-1.495 1.496A7.95 7.95 0 0 1 24 16zm-2.586-6L22 8.586L17.285 13.3A3 3 0 0 0 16 13a3 3 0 1 0 3 3a3 3 0 0 0-.3-1.285zM16 17a1 1 0 1 1 1-1a1 1 0 0 1-1 1m0-9a8 8 0 0 1 3.122.635l1.496-1.496A9.986 9.986 0 0 0 6 16h2a8.01 8.01 0 0 1 8-8'/%3E%3Cpath fill='currentColor' d='M16 30a14 14 0 1 1 14-14a14.016 14.016 0 0 1-14 14m0-26a12 12 0 1 0 12 12A12.014 12.014 0 0 0 16 4'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-network-4,[i-carbon-network-4=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Ccircle cx='21' cy='26' r='2' fill='currentColor'/%3E%3Ccircle cx='21' cy='6' r='2' fill='currentColor'/%3E%3Ccircle cx='4' cy='16' r='2' fill='currentColor'/%3E%3Cpath fill='currentColor' d='M28 12a3.996 3.996 0 0 0-3.858 3h-4.284a3.966 3.966 0 0 0-5.491-2.643l-3.177-3.97A3.96 3.96 0 0 0 12 6a4 4 0 1 0-4 4a4 4 0 0 0 1.634-.357l3.176 3.97a3.924 3.924 0 0 0 0 4.774l-3.176 3.97A4 4 0 0 0 8 22a4 4 0 1 0 4 4a3.96 3.96 0 0 0-.81-2.387l3.176-3.97A3.966 3.966 0 0 0 19.858 17h4.284A3.993 3.993 0 1 0 28 12M6 6a2 2 0 1 1 2 2a2 2 0 0 1-2-2m2 22a2 2 0 1 1 2-2a2 2 0 0 1-2 2m8-10a2 2 0 1 1 2-2a2 2 0 0 1-2 2m12 0a2 2 0 1 1 2-2a2 2 0 0 1-2 2'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-renew,[i-carbon-renew=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M12 10H6.78A11 11 0 0 1 27 16h2A13 13 0 0 0 6 7.68V4H4v8h8zm8 12h5.22A11 11 0 0 1 5 16H3a13 13 0 0 0 23 8.32V28h2v-8h-8z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-search,[i-carbon-search=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='m29 27.586l-7.552-7.552a11.018 11.018 0 1 0-1.414 1.414L27.586 29ZM4 13a9 9 0 1 1 9 9a9.01 9.01 0 0 1-9-9'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-side-panel-close,[i-carbon-side-panel-close=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M28 4H4c-1.1 0-2 .9-2 2v20c0 1.1.9 2 2 2h24c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2M10 26H4V6h6zm18-11H17.8l3.6-3.6L20 10l-6 6l6 6l1.4-1.4l-3.6-3.6H28v9H12V6h16z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-side-panel-open,[i-carbon-side-panel-open=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M28 4H4c-1.1 0-2 .9-2 2v20c0 1.1.9 2 2 2h24c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2M10 26H4V6h6zm18 0H12v-9h10.2l-3.6 3.6L20 22l6-6l-6-6l-1.4 1.4l3.6 3.6H12V6h16z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-sun,[i-carbon-sun=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M16 12.005a4 4 0 1 1-4 4a4.005 4.005 0 0 1 4-4m0-2a6 6 0 1 0 6 6a6 6 0 0 0-6-6M5.394 6.813L6.81 5.399l3.505 3.506L8.9 10.319zM2 15.005h5v2H2zm3.394 10.193L8.9 21.692l1.414 1.414l-3.505 3.506zM15 25.005h2v5h-2zm6.687-1.9l1.414-1.414l3.506 3.506l-1.414 1.414zm3.313-8.1h5v2h-5zm-3.313-6.101l3.506-3.506l1.414 1.414l-3.506 3.506zM15 2.005h2v5h-2z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-text-wrap,[i-carbon-text-wrap=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M4 23h8v2H4zm20.523-9H4v2h20.5a3.5 3.5 0 0 1 0 7h-5.672l2.586-2.586L20 19l-5 5l5 5l1.414-1.414L18.828 25h5.705a5.5 5.5 0 0 0-.01-11M4 5h24v2H4z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-time,[i-carbon-time=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M16 30a14 14 0 1 1 14-14a14 14 0 0 1-14 14m0-26a12 12 0 1 0 12 12A12 12 0 0 0 16 4'/%3E%3Cpath fill='currentColor' d='M20.59 22L15 16.41V7h2v8.58l5 5.01z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-view{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M30.94 15.66A16.69 16.69 0 0 0 16 5A16.69 16.69 0 0 0 1.06 15.66a1 1 0 0 0 0 .68A16.69 16.69 0 0 0 16 27a16.69 16.69 0 0 0 14.94-10.66a1 1 0 0 0 0-.68M16 25c-5.3 0-10.9-3.93-12.93-9C5.1 10.93 10.7 7 16 7s10.9 3.93 12.93 9C26.9 21.07 21.3 25 16 25'/%3E%3Cpath fill='currentColor' d='M16 10a6 6 0 1 0 6 6a6 6 0 0 0-6-6m0 10a4 4 0 1 1 4-4a4 4 0 0 1-4 4'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon-view-off{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='m5.24 22.51l1.43-1.42A14.06 14.06 0 0 1 3.07 16C5.1 10.93 10.7 7 16 7a12.4 12.4 0 0 1 4 .72l1.55-1.56A14.7 14.7 0 0 0 16 5A16.69 16.69 0 0 0 1.06 15.66a1 1 0 0 0 0 .68a16 16 0 0 0 4.18 6.17'/%3E%3Cpath fill='currentColor' d='M12 15.73a4 4 0 0 1 3.7-3.7l1.81-1.82a6 6 0 0 0-7.33 7.33zm18.94-.07a16.4 16.4 0 0 0-5.74-7.44L30 3.41L28.59 2L2 28.59L3.41 30l5.1-5.1A15.3 15.3 0 0 0 16 27a16.69 16.69 0 0 0 14.94-10.66a1 1 0 0 0 0-.68M20 16a4 4 0 0 1-6 3.44L19.44 14a4 4 0 0 1 .56 2m-4 9a13.05 13.05 0 0 1-6-1.58l2.54-2.54a6 6 0 0 0 8.35-8.35l2.87-2.87A14.54 14.54 0 0 1 28.93 16C26.9 21.07 21.3 25 16 25'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-carbon\:warning-square,[i-carbon\:warning-square=""]{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M16 20a1.5 1.5 0 1 0 1.5 1.5A1.5 1.5 0 0 0 16 20M15 9h2v9h-2z'/%3E%3Cpath fill='currentColor' d='M26 28H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h20a2 2 0 0 1 2 2v20a2 2 0 0 1-2 2M6 6v20h20.001L26 6Z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-catppuccin-angular{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cg fill='none' fill-rule='evenodd' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath stroke='%23ed8796' d='m8 1l6.5 2l-1 9.5L8 15l-5.5-2.5l-1-9.5z'/%3E%3Cpath stroke='%23cad3f5' d='m4.5 10.5l3.5-7l3.5 7m-5.796-2h4.635'/%3E%3C/g%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-catppuccin-css{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cg fill='none' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath stroke='%238aadf4' d='M1.5 1.5h13L13 13l-5 2l-5-2z'/%3E%3Cpath stroke='%23cad3f5' d='M5 4.5h6l-.5 6l-2.5 1l-2.5-1l-.08-1m1.08-2h4'/%3E%3C/g%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-catppuccin-file{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='none' stroke='%23cad3f5' stroke-linecap='round' stroke-linejoin='round' d='M13.5 6.5v6a2 2 0 0 1-2 2h-7a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h4.01m-.01 0l5 5h-4a1 1 0 0 1-1-1z'/%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-catppuccin-folder-node-open{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cg fill='none' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath stroke='%23cad3f5' d='m1.875 8l.686-2.743a1 1 0 0 1 .97-.757h10.938a1 1 0 0 1 .97 1.243l-.315 1.26M6 13.5H2.004A1.5 1.5 0 0 1 .5 12V3.5a1 1 0 0 1 1-1h5a1 1 0 0 1 1 1v1'/%3E%3Cpath stroke='%23a6da95' d='m12.5 8.576l3 1.714v3.42l-3 1.714l-3-1.714v-3.42z'/%3E%3C/g%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-catppuccin-html{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cg fill='none' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath stroke='%23f5a97f' d='M1.5 1.5h13L13 13l-5 2l-5-2z'/%3E%3Cpath stroke='%23cad3f5' d='M11 4.5H5l.25 3h5.5l-.25 3l-2.5 1l-2.5-1l-.08-1'/%3E%3C/g%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-catppuccin-javascript{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cg fill='none' stroke='%23eed49f' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M4.5 11a1.5 1.5 0 0 0 3 0V7.5m5 1.25c0-.69-.537-1.25-1.2-1.25h-.6c-.663 0-1.2.56-1.2 1.25S10.037 10 10.7 10h.6c.663 0 1.2.56 1.2 1.25s-.537 1.25-1.2 1.25h-.6c-.663 0-1.2-.56-1.2-1.25'/%3E%3Cpath d='M4 1.5h8c1.385 0 2.5 1.115 2.5 2.5v8c0 1.385-1.115 2.5-2.5 2.5H4A2.495 2.495 0 0 1 1.5 12V4c0-1.385 1.115-2.5 2.5-2.5'/%3E%3C/g%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-catppuccin-javascript-react{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cg fill='none' stroke='%2391d7e3' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M8 10.8c4.14 0 7.5-1.25 7.5-2.8S12.14 5.2 8 5.2S.5 6.45.5 8s3.36 2.8 7.5 2.8'/%3E%3Cpath d='M5.52 9.4c2.07 3.5 4.86 5.72 6.23 4.95c1.37-.78.8-4.24-1.27-7.75C8.41 3.1 5.62.88 4.25 1.65c-1.37.78-.8 4.24 1.27 7.75'/%3E%3Cpath d='M5.52 6.6c-2.07 3.5-2.64 6.97-1.27 7.75c1.37.77 4.16-1.45 6.23-4.95s2.64-6.97 1.27-7.75C10.38.88 7.59 3.1 5.52 6.6'/%3E%3Cpath d='M8.5 8a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5'/%3E%3C/g%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-catppuccin-json{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='none' stroke='%23eed49f' stroke-linecap='round' stroke-linejoin='round' d='M4.5 2.5H4c-.75 0-1.5.75-1.5 1.5v2c0 1.1-1 2-1.83 2c.83 0 1.83.9 1.83 2v2c0 .75.75 1.5 1.5 1.5h.5m7-11h.5c.75 0 1.5.75 1.5 1.5v2c0 1.1 1 2 1.83 2c-.83 0-1.83.9-1.83 2v2c0 .74-.75 1.5-1.5 1.5h-.5m-6.5-3a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1m3 0a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1m3 0a.5.5 0 1 0 0-1a.5.5 0 0 0 0 1'/%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-catppuccin-less{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='none' stroke='%238aadf4' stroke-linecap='round' stroke-linejoin='round' d='M4 2.5c-.74 0-1.5.76-1.5 1.5v2c0 1.1-1.1 2-1.83 2c.74 0 1.83.9 1.83 2v2c0 .74.76 1.5 1.5 1.5m1.5-8v5a1 1 0 0 0 1 1H7m4.5-4c0-.69-.59-1-1.25-1h-.5c-.66 0-1.25.56-1.25 1.25S9.09 9 9.75 9h.5c.66 0 1.25.56 1.25 1.25s-.59 1.25-1.25 1.25h-.5c-.66 0-1.25-.31-1.25-1m3.5-8c.74 0 1.5.76 1.5 1.5v2c0 1.1 1.1 2 1.83 2c-.74 0-1.83.9-1.83 2v2c0 .74-.76 1.5-1.5 1.5'/%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-catppuccin-markdown{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='none' stroke='%237dc4e4' stroke-linecap='round' stroke-linejoin='round' d='m9.25 8.25l2.25 2.25l2.25-2.25M3.5 11V5.5l2.04 3l1.96-3V11m4-.5V5M1.65 2.5h12.7c.59 0 1.15.49 1.15 1v9c0 .51-.56 1-1.15 1H1.65c-.59 0-1.15-.49-1.15-1V3.58c0-.5.56-1.08 1.15-1.08'/%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-catppuccin-svelte{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cg fill='none' stroke='%23f5a97f' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M12.86 6.72s1.39-1.98.08-3.87C11.286.763 9.44 1.6 9.44 1.6S6.15 3.35 4.33 4.59c-1.4 1-2.24 2.26-1.03 4.37c1.22 2.1 4.58 1.21 4.58 1.21'/%3E%3Cpath d='M3.14 9.28s-1.39 1.98-.08 3.87c1.31 1.9 3.5 1.24 3.5 1.24s3.29-1.74 5.11-2.98c1.4-1 2.24-2.26 1.03-4.37c-1.22-2.1-4.58-1.21-4.58-1.21M6.3 6.96l4.14-2.56m-4.92 7.25L9.66 9.1'/%3E%3C/g%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-catppuccin-svg{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='none' stroke='%23f5a97f' stroke-linecap='round' stroke-linejoin='round' d='m4.54 10l6.92-4m-6.92 4a1.5 1.5 0 1 0-2.6 1.5a1.5 1.5 0 0 0 2.6-1.5M8 4v8m0-8a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3M4.54 6l6.92 4M4.54 6a1.5 1.5 0 1 0-2.6-1.5A1.5 1.5 0 0 0 4.54 6M8 12a1.5 1.5 0 1 0 0 3a1.5 1.5 0 0 0 0-3m3.46-2a1.5 1.5 0 1 0 2.6 1.5a1.5 1.5 0 0 0-2.6-1.5m0-4a1.5 1.5 0 1 0 2.6-1.5a1.5 1.5 0 0 0-2.6 1.5'/%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-catppuccin-toml{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='none' stroke='%23ee99a0' stroke-linecap='round' stroke-linejoin='round' d='M3.5 1.5h-2v13h2m9-13h2v13h-2m-8-11h7v3h-2v6h-3v-6h-2z'/%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-catppuccin-typescript{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cg fill='none' stroke='%238aadf4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M4 1.5h8A2.5 2.5 0 0 1 14.5 4v8a2.5 2.5 0 0 1-2.5 2.5H4A2.5 2.5 0 0 1 1.5 12V4A2.5 2.5 0 0 1 4 1.5'/%3E%3Cpath d='M12.5 8.75c0-.69-.54-1.25-1.2-1.25h-.6c-.66 0-1.2.56-1.2 1.25S10.04 10 10.7 10h.6c.66 0 1.2.56 1.2 1.25s-.54 1.25-1.2 1.25h-.6c-.66 0-1.2-.56-1.2-1.25m-3-3.75v5M5 7.5h3'/%3E%3C/g%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-catppuccin-typescript-react{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cg fill='none' stroke='%238aadf4' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M8 11.3c4.14 0 7.5-1.28 7.5-2.86S12.14 5.58 8 5.58S.5 6.86.5 8.44s3.36 2.87 7.5 2.87Z'/%3E%3Cpath d='M5.52 9.87c2.07 3.6 4.86 5.86 6.23 5.07c1.37-.8.8-4.34-1.27-7.93S5.62 1.16 4.25 1.95s-.8 4.34 1.27 7.92'/%3E%3Cpath d='M5.52 7.01c-2.07 3.59-2.64 7.14-1.27 7.93s4.16-1.48 6.23-5.07c2.07-3.58 2.64-7.13 1.27-7.92c-1.37-.8-4.16 1.47-6.23 5.06'/%3E%3Cpath d='M8.5 8.44a.5.5 0 0 1-.5.5a.5.5 0 0 1-.5-.5a.5.5 0 0 1 .5-.5a.5.5 0 0 1 .5.5'/%3E%3C/g%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-catppuccin-vue{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cg fill='none' stroke='%23a6da95' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M1 1.5h5.44L8 4.56L9.56 1.5H15l-6.99 13z'/%3E%3Cpath d='M12.05 1.73L8 9.28L3.95 1.73'/%3E%3C/g%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-catppuccin-yaml{background:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 16 16' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='none' stroke='%23ed8796' stroke-linecap='round' stroke-linejoin='round' d='M2.5 1.5h3l3 4l3-4h3l-9 13h-3L7 8z'/%3E%3C/svg%3E") no-repeat;background-size:100% 100%;background-color:transparent;display:block;width:1.2em;height:1.2em;}.i-ic-outline-circle{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 24 24' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10s10-4.47 10-10S17.53 2 12 2m0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8s8 3.58 8 8s-3.58 8-8 8'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-ic-outline-hexagon{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 24 24' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M17.2 3H6.8l-5.2 9l5.2 9h10.4l5.2-9zm-1.15 16h-8.1l-4.04-7l4.04-7h8.09l4.04 7z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.i-ic-outline-square{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 24 24' display='block' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='M3 3v18h18V3zm16 16H5V5h14z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:block;width:1.2em;height:1.2em;}.container{width:100%;}.icon-btn,[icon-btn=""]{display:inline-block;cursor:pointer;-webkit-user-select:none;user-select:none;border-radius:0.5rem;padding:0.5rem;font-size:0.9em;opacity:0.75;outline:2px solid transparent !important;outline-offset:2px !important;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;transition-duration:200ms;}.border-main,.children\:border-main>*,[border~="main"]{--un-border-opacity:1;border-color:rgb(156 163 175 / var(--un-border-opacity));--un-border-opacity:0.3;}[border~="subtle"]{--un-border-opacity:1;border-color:rgb(156 163 175 / var(--un-border-opacity));--un-border-opacity:0.1;}.bg-active{background-color:rgb(156 163 175 / 0.1) /* #9ca3af */;}.bg-main,[bg-main=""]{--un-bg-opacity:1;background-color:rgb(255 255 255 / var(--un-bg-opacity)) /* #fff */;}.dark .bg-main,.dark [bg-main=""]{--un-bg-opacity:1;background-color:rgb(18 18 18 / var(--un-bg-opacity)) /* #121212 */;}.bg-subtle,[bg-subtle=""]{background-color:rgb(156 163 175 / 0.03) /* #9ca3af */;}.hover\:bg-active:hover{background-color:rgb(156 163 175 / 0.1) /* #9ca3af */;}.icon-btn:hover{background-color:rgb(156 163 175 / 0.1) /* #9ca3af */;--un-text-opacity:1;color:rgb(13 148 136 / var(--un-text-opacity)) /* #0d9488 */;opacity:1;}[icon-btn=""]:hover{background-color:rgb(156 163 175 / 0.1) /* #9ca3af */;--un-text-opacity:1;color:rgb(13 148 136 / var(--un-text-opacity)) /* #0d9488 */;opacity:1;}.dark .status-green,.dark [status-green=""]{--un-text-opacity:1;color:rgb(52 230 118 / var(--un-text-opacity)) /* #34E676 */;}.status-green,[status-green=""]{--un-text-opacity:1;color:rgb(21 128 61 / var(--un-text-opacity)) /* #15803d */;}.dark .status-red,.dark [status-red=""]{--un-text-opacity:1;color:rgb(239 83 80 / var(--un-text-opacity)) /* #EF5350 */;}.status-red,[status-red=""]{--un-text-opacity:1;color:rgb(183 28 28 / var(--un-text-opacity)) /* #b71c1c */;}.dark .status-yellow{--un-text-opacity:1;color:rgb(234 179 6 / var(--un-text-opacity)) /* #EAB306 */;}.status-yellow{--un-text-opacity:1;color:rgb(130 119 23 / var(--un-text-opacity)) /* #827717 */;}@media (min-width: 640px){.container{max-width:640px;}}@media (min-width: 768px){.container{max-width:768px;}}@media (min-width: 1024px){.container{max-width:1024px;}}@media (min-width: 1280px){.container{max-width:1280px;}}@media (min-width: 1536px){.container{max-width:1536px;}}.pointer-events-none{pointer-events:none;}.absolute,[absolute=""]{position:absolute;}.fixed,[pos~="fixed"]{position:fixed;}.relative{position:relative;}.bottom-0,[pos~="bottom-0"]{bottom:0;}.bottom-3,[bottom-3=""]{bottom:0.75rem;}.left-0,[pos~="left-0"]{left:0;}.left-3,[left-3=""]{left:0.75rem;}.right-0,[pos~="right-0"]{right:0;}.right-3,[right-3=""]{right:0.75rem;}.top-0,[pos~="top-0"]{top:0;}.grid,[grid~="\~"]{display:grid;}.cols-\[max-content_1fr\],[grid~="cols-\[max-content_1fr\]"]{grid-template-columns:max-content 1fr;}.grid-cols-\[1fr_max-content_max-content_max-content_max-content_max-content_1fr\]{grid-template-columns:1fr max-content max-content max-content max-content max-content 1fr;}.rows-\[min-content_1fr\],[grid~="rows-\[min-content_1fr\]"]{grid-template-rows:min-content 1fr;}.grid-rows-1{grid-template-rows:repeat(1,minmax(0,1fr));}.m-auto,.ma,[ma=""]{margin:auto;}.children\:my-auto>*,.my-auto,[children\:my-auto=""]>*,[my-auto=""]{margin-top:auto;margin-bottom:auto;}.my3,[my3=""]{margin-top:0.75rem;margin-bottom:0.75rem;}.mb-4{margin-bottom:1rem;}.ml-0\.4,[ml-0\.4=""]{margin-left:0.1rem;}.ml-1,.ml1{margin-left:0.25rem;}.ml-8{margin-left:2rem;}.mr1\.5,[mr1\.5=""]{margin-right:0.375rem;}.ms{margin-inline-start:1rem;}.mt-2,.mt2,[mt2=""]{margin-top:0.5rem;}.block,[block=""]{display:block;}[size~="\31 00"]{width:25rem;height:25rem;}.h-\[calc\(100vh-55px\)\]{height:calc(100vh - 55px);}.h-100vh,.h-screen,[h-100vh=""],[size~="h-screen"]{height:100vh;}.h-1px,[h-1px=""]{height:1px;}.h-200{height:50rem;}.h-3,[h-3=""]{height:0.75rem;}.h-40,[h-40=""]{height:10rem;}.h-54px,[h-54px=""]{height:54px;}.h-full,[h-full=""]{height:100%;}.h-max,[h-max=""]{height:max-content;}.h-min{height:min-content;}.h-unset,[h-unset=""]{height:unset;}.min-h-screen{min-height:100vh;}.min-w-70px,[min-w-70px=""]{min-width:70px;}.w-\[calc\(100vw-100px\)\]{width:calc(100vw - 100px);}.w-1,[w-1=""]{width:0.25rem;}.w-100\%,.w-full,[w-full=""]{width:100%;}.w-3,[w-3=""]{width:0.75rem;}.w-38,[w-38=""]{width:9.5rem;}.w-screen,[size~="w-screen"]{width:100vw;}[min-h-screen~="default\:"]:default{min-height:100vh;}.flex,[flex=""],[flex~="\~"]{display:flex;}.flex-auto,[flex-auto=""]{flex:1 1 auto;}.flex-none,[flex-none=""],[flex~="none"]{flex:none;}.flex-col,[flex-col=""],[flex~="col"]{flex-direction:column;}[flex~="wrap"]{flex-wrap:wrap;}.translate-x-0{--un-translate-x:0;transform:translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));}.translate-x-1\/2{--un-translate-x:50%;transform:translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));}.rotate-45{--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-rotate:45deg;transform:translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));}.scale-85{--un-scale-x:0.85;--un-scale-y:0.85;transform:translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));}.transform{transform:translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));}.cursor-pointer{cursor:pointer;}.select-none,[select-none=""]{-webkit-user-select:none;user-select:none;}.items-center,[flex~="items-center"],[items-center=""]{align-items:center;}.gap-1,[flex~="gap-1"]{gap:0.25rem;}.gap-2,.gap2,[flex~="gap-2"],[flex~="gap2"]{gap:0.5rem;}.gap-4,[flex~="gap-4"]{gap:1rem;}.gap-x-4,[grid~="gap-x-4"]{column-gap:1rem;}.gap-y-1,[grid~="gap-y-1"]{row-gap:0.25rem;}.of-auto,.overflow-auto,[of-auto=""]{overflow:auto;}.of-hidden,.overflow-hidden,[of-hidden=""],[overflow-hidden=""]{overflow:hidden;}.overflow-y-auto,[overflow-y-auto=""]{overflow-y:auto;}.text-truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}.whitespace-nowrap,.ws-nowrap,[ws-nowrap=""]{white-space:nowrap;}.b,.border,[border~="\~"]{border-width:1px;}.border-b,.children\:border-b>*,[border~="b"]{border-bottom-width:1px;}[border~="l"]{border-left-width:1px;}[border~="r"]{border-right-width:1px;}[border~="t"]{border-top-width:1px;}.rounded,[border~="rounded"],[rounded=""]{border-radius:0.25rem;}.rounded-full,[rounded-full=""]{border-radius:9999px;}.border-none{border-style:none;}.bg-black\:50,[bg-black\:50=""]{background-color:rgb(0 0 0 / 0.5) /* #000 */;}.bg-green-400\/15{background-color:rgb(74 222 128 / 0.15) /* #4ade80 */;}.bg-green-400\/30{background-color:rgb(74 222 128 / 0.3) /* #4ade80 */;}.bg-red-400\/15{background-color:rgb(248 113 113 / 0.15) /* #f87171 */;}.bg-red-400\/30{background-color:rgb(248 113 113 / 0.3) /* #f87171 */;}.bg-transparent{background-color:transparent /* transparent */;}.bg-opacity-75,[bg-opacity-75=""]{--un-bg-opacity:0.75;}.p0\!{padding:0 !important;}.p1,[p1=""]{padding:0.25rem;}.p2,[p2=""]{padding:0.5rem;}.p3,[p3=""]{padding:0.75rem;}.p4,[p4=""]{padding:1rem;}.children\:px-4>*,.px{padding-left:1rem;padding-right:1rem;}.children\:py-2>*,.py-2,[py-2=""]{padding-top:0.5rem;padding-bottom:0.5rem;}.px-1\.5{padding-left:0.375rem;padding-right:0.375rem;}.px-2,[px-2=""]{padding-left:0.5rem;padding-right:0.5rem;}.px-3{padding-left:0.75rem;padding-right:0.75rem;}.px-6,[px-6=""]{padding-left:1.5rem;padding-right:1.5rem;}.py-1{padding-top:0.25rem;padding-bottom:0.25rem;}.py-4,[py-4=""]{padding-top:1rem;padding-bottom:1rem;}.pl-4,[pl-4=""]{padding-left:1rem;}.pr-4,[pr-4=""]{padding-right:1rem;}.text-center,[text-center=""]{text-align:center;}.text-left,[text-left=""]{text-align:left;}.text-right,[text-right=""]{text-align:right;}.children\:align-middle>*{vertical-align:middle;}.text-11px{font-size:11px;}.text-lg,[text-lg=""]{font-size:1.125rem;line-height:1.75rem;}.text-sm,[text-sm=""]{font-size:0.875rem;line-height:1.25rem;}.text-xl,[text-xl=""]{font-size:1.25rem;line-height:1.75rem;}.text-xs,[text-xs=""]{font-size:0.75rem;line-height:1rem;}.dark .dark\:text-orange-2{--un-text-opacity:1;color:rgb(254 215 170 / var(--un-text-opacity)) /* #fed7aa */;}.dark .dark\:text-orange-4{--un-text-opacity:1;color:rgb(251 146 60 / var(--un-text-opacity)) /* #fb923c */;}.dark [text~="dark\:gray-200"]{--un-text-opacity:1;color:rgb(229 231 235 / var(--un-text-opacity)) /* #e5e7eb */;}.text-gray\:50{color:rgb(156 163 175 / 0.5) /* #9ca3af */;}.text-orange-5{--un-text-opacity:1;color:rgb(249 115 22 / var(--un-text-opacity)) /* #f97316 */;}.text-orange-9{--un-text-opacity:1;color:rgb(124 45 18 / var(--un-text-opacity)) /* #7c2d12 */;}[text~="gray-700"]{--un-text-opacity:1;color:rgb(55 65 81 / var(--un-text-opacity)) /* #374151 */;}.dark .dark\:fw-unset,.dark [dark\:fw-unset=""]{font-weight:unset;}.font-bold{font-weight:700;}.font-light,[font-light=""]{font-weight:300;}.fw-600,[fw-600=""]{font-weight:600;}.leading-1\.6em{line-height:1.6em;}.tracking-widest,[tracking-widest=""]{letter-spacing:0.1em;}.font-mono,[font-mono=""]{font-family:"DM Mono",ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;}.italic,[italic=""]{font-style:italic;}.underline{text-decoration-line:underline;}.hover\:underline:hover{text-decoration-line:underline;}[hover~="underline"]:hover{text-decoration-line:underline;}.dark .dark\:op50,.dark [dark\:op50=""],.op50,[op50=""]{opacity:0.5;}.op20,[op20=""]{opacity:0.2;}.op40,[op40=""]{opacity:0.4;}.op70,[op70=""]{opacity:0.7;}.op72,[op72=""]{opacity:0.72;}.op75,.opacity-75,[op75=""]{opacity:0.75;}.opacity-0{opacity:0;}.opacity-100{opacity:1;}.opacity-25{opacity:0.25;}.shadow,[shadow=""]{--un-shadow:var(--un-shadow-inset) 0 1px 3px 0 var(--un-shadow-color, rgb(0 0 0 / 0.1)),var(--un-shadow-inset) 0 1px 2px -1px var(--un-shadow-color, rgb(0 0 0 / 0.1));box-shadow:var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);}.shadow-lg{--un-shadow:var(--un-shadow-inset) 0 10px 15px -3px var(--un-shadow-color, rgb(0 0 0 / 0.1)),var(--un-shadow-inset) 0 4px 6px -4px var(--un-shadow-color, rgb(0 0 0 / 0.1));box-shadow:var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);}.\!outline-none{outline:2px solid transparent !important;outline-offset:2px !important;}.outline-none,[outline-none=""]{outline:2px solid transparent;outline-offset:2px;}.backdrop-blur-8,[backdrop-blur-8=""]{--un-backdrop-blur:blur(8px);-webkit-backdrop-filter:var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);backdrop-filter:var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);}.brightness-60{--un-brightness:brightness(0.6);filter:var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);}.hue-rotate-180{--un-hue-rotate:hue-rotate(180deg);filter:var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);}.invert-100{--un-invert:invert(1);filter:var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);}.saturate-200{--un-saturate:saturate(2);filter:var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);}.saturate-50{--un-saturate:saturate(0.5);filter:var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);}.transition-all,[transition-all=""]{transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;}.transition-transform{transition-property:transform;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;}.duration-300{transition-duration:300ms;} 