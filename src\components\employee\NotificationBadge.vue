<template>
  <div class="flex gap-1.5 items-center self-stretch my-auto min-h-6">
    <component :is="iconComponent" class="w-6 h-6" />
    <span class="self-stretch my-auto">
      <strong>{{ count }}</strong> {{ label }}
    </span>
  </div>
</template>

<script setup lang="ts">
interface Props {
  count: number
  label: string
  icon: 'leads' | 'messages'
}

const props = defineProps<Props>()

const iconComponent = computed(() => {
  // Simple icon placeholders - replace with actual icon components
  return props.icon === 'leads' ? '📊' : '💬'
})
</script>
