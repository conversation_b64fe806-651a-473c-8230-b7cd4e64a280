"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const handleIgnorePatternsScope = (ignorePatterns, config) => {
  config.ignores = ignorePatterns;
};
const readIgnorePatternsFromConfig = (config) => {
  return "ignorePatterns" in config && Array.isArray(config.ignorePatterns) ? config.ignorePatterns : void 0;
};
exports.handleIgnorePatternsScope = handleIgnorePatternsScope;
exports.readIgnorePatternsFromConfig = readIgnorePatternsFromConfig;
