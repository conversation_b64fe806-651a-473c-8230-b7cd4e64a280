<template>
  <div class="p-6">
    <!-- Dashboard Tabs -->
    <DashboardTabs
      :active-tab="activeTab"
      :tabs="dashboardTabs"
      @tab-change="handleTabChange"
    />

    <!-- Welcome Section -->
    <WelcomeSection
      :user-name="currentUser.name"
      :time-period="selectedTimePeriod"
      @time-period-change="handleTimePeriodChange"
    />

    <!-- Dashboard Content -->
    <div class="space-y-6">
      <!-- Top Lead Providers -->
      <TopLeadProviders :providers="dashboardData.topProviders" />

      <!-- Stats Grid -->
      <StatsGrid :stats="dashboardData.stats" />

      <!-- Top Leads Comparison -->
      <TopLeadsComparison :comparison-data="dashboardData.leadsComparison" />

      <!-- Lead Status Table -->
      <LeadStatusTable :leads="dashboardData.leads" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import DashboardTabs from '@/components/employee/DashboardTabs.vue'
import WelcomeSection from '@/components/employee/WelcomeSection.vue'
import TopLeadProviders from '@/components/employee/TopLeadProviders.vue'
import StatsGrid from '@/components/employee/StatsGrid.vue'
import TopLeadsComparison from '@/components/employee/TopLeadsComparison.vue'
import LeadStatusTable from '@/components/employee/LeadStatusTable.vue'

interface User {
  id: string
  name: string
  avatar?: string
}

interface DashboardData {
  newLeadsCount: number
  messagesCount: number
  topProviders: any[]
  stats: any[]
  leadsComparison: any[]
  leads: any[]
}

// State
const currentUser = ref<User>({
  id: '1',
  name: 'John Doe'
})

const activeTab = ref('lead-analysis')
const selectedTimePeriod = ref('today')

const dashboardTabs = [
  { id: 'lead-analysis', label: 'Lead Analysis' },
  { id: 'new-loan-summary', label: 'New Loan Summary' },
  { id: 'cpa-lead-provider', label: 'CPA by Lead Provider' },
  { id: 'promise-to-pay', label: 'Promise to Pay Summary' },
  { id: 'collection-age', label: 'Collection Age Bucket' },
  { id: 'defaulted-payments', label: 'Defaulted vs. Payments Report' }
]

const dashboardData = reactive<DashboardData>({
  newLeadsCount: 28,
  messagesCount: 3,
  topProviders: [],
  stats: [],
  leadsComparison: [],
  leads: []
})

// Event Handlers
const handleTabChange = (tabId: string) => {
  activeTab.value = tabId
  // Load data for the selected tab
  loadTabData(tabId)
}

const handleTimePeriodChange = (period: string) => {
  selectedTimePeriod.value = period
  // Reload dashboard data for the selected period
  loadDashboardData()
}

// Data Loading
const loadDashboardData = () => {
  // Implement data loading logic
  console.log('Loading dashboard data for period:', selectedTimePeriod.value)
}

const loadTabData = (tabId: string) => {
  // Implement tab-specific data loading
  console.log('Loading data for tab:', tabId)
}

// Initialize
loadDashboardData()
</script>
