<template>
  <div class="flex gap-1.5 self-stretch my-auto font-bold cursor-pointer" @click="handleClick">
    <img
      :src="user.avatar || defaultAvatar"
      class="object-contain shrink-0 w-6 rounded-full aspect-square"
      :alt="`${user.name} avatar`"
    />
    <span class="my-auto">{{ user.name }}</span>
    <svg class="w-3.5 h-3.5 my-auto" viewBox="0 0 24 24" fill="currentColor">
      <path d="M7 10l5 5 5-5z"/>
    </svg>
  </div>
</template>

<script setup lang="ts">
interface User {
  id: string
  name: string
  avatar?: string
}

interface Props {
  user: User
}

interface Emits {
  profileClick: [user: User]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const defaultAvatar = 'https://cdn.builder.io/api/v1/image/assets/TEMP/6e660936d2efcdbfb8b39739e13236f08ab20a85?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde'

const handleClick = () => {
  emit('profileClick', props.user)
}
</script>
