declare const _default: {
    configs: {
        "flat/pedantic": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/suspicious": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/style": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/restriction": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/correctness": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/perf": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/eslint": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/import": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/jest": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/jsdoc": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/jsx-a11y": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/nextjs": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/node": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/promise": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/react": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/react-hooks": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/react-perf": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/typescript": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/unicorn": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        "flat/vitest": import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        recommended: import("eslint").Linter.LegacyConfig<Record<string, "off">, Record<string, "off">>;
        all: import("eslint").Linter.LegacyConfig<Record<string, "off">, Record<string, "off">>;
        'flat/all': import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
        'flat/recommended': import("eslint").Linter.Config<import("eslint").Linter.RulesRecord>[];
    };
    buildFromOxlintConfig: (config: import('./build-from-oxlint-config/types.js').OxlintConfig) => import('./build-from-oxlint-config/types.js').EslintPluginOxlintConfig[];
    buildFromOxlintConfigFile: (oxlintConfigFile: string) => import('./build-from-oxlint-config/types.js').EslintPluginOxlintConfig[];
};
export default _default;
